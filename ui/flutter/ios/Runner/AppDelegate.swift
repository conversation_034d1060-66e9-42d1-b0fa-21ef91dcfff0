/**
 * FILE: AppDelegate.swift
 *
 * DESCRIPTION:
 *     iOS AppDelegate with Platform Channel integration.
 *     Initializes universal Platform Channel handler from ItForceCore.
 *     Uses a single FlutterEngine instance to avoid lifecycle crashes.
 *
 * AUTHOR: wei
 * HISTORY:
 *     23/06/2025 - integrate universal Platform Channel handler
 *     01/07/2025 - fix engine binding crash and plugin registration issue
 *     02/07/2025 - unify FlutterEngine management to fix crash on app restart
 */

import Flutter
import UIKit
import ItForceCore

@main
@objc public class AppDelegate: FlutterAppDelegate {

    /// 全局FlutterEngine，保证生命周期贯穿整个App
    private lazy var flutterEngine: FlutterEngine = {
        let engine = FlutterEngine(name: "itforce_engine")
        engine.run()
        GeneratedPluginRegistrant.register(with: engine)
        // NSLog("🔥 [AppDelegate] FlutterEngine initialized and plugins registered") // Debug NSLog commented for production
        return engine
    }()

    private var platformChannelHandler: PlatformChannelHandler?

    public override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        // NSLog("🔥 [AppDelegate] didFinishLaunchingWithOptions called") // Debug NSLog commented for production

        // 使用 FlutterEngine 创建 FlutterViewController 并作为 root
        if window == nil {
            window = UIWindow(frame: UIScreen.main.bounds)
        }

        let flutterViewController = FlutterViewController(engine: flutterEngine, nibName: nil, bundle: nil)
        window?.rootViewController = flutterViewController
        window?.makeKeyAndVisible()

        // Call super after setting up FlutterViewController
        let result = super.application(application, didFinishLaunchingWithOptions: launchOptions)

        // 异步设置初始化通道，等待 UI 完成加载
        DispatchQueue.main.async {
            self.setupInitializationChannel()
        }

        return result
    }

    /// 初始化时只注册一个简单的 init channel，等待用户登录时再创建详细 handler
    private func setupInitializationChannel() {
        guard let controller = findFlutterViewController() else {
            // NSLog("❌ [AppDelegate] FlutterViewController not found") // Debug NSLog commented for production
            return
        }

        let messenger = controller.engine.binaryMessenger

        let initChannel = FlutterMethodChannel(
            name: "panabit_client/init",
            binaryMessenger: messenger
        )

        initChannel.setMethodCallHandler { [weak self] call, result in
            if call.method == "initializeBackend" {
                // NSLog("🔥 Received initializeBackend") // Debug NSLog commented for production

                if let handler = self?.platformChannelHandler {
                    // NSLog("✅ Reusing existing PlatformChannelHandler") // Debug NSLog commented for production
                    Task {
                        await handler.handleInitializeBackendFromAppDelegate(result: result as Any)
                    }
                } else {
                    // NSLog("🔧 Creating new PlatformChannelHandler") // Debug NSLog commented for production
                    if let handler = self?.setupPlatformChannelHandler() {
                        Task {
                            await handler.handleInitializeBackendFromAppDelegate(result: result as Any)
                        }
                    } else {
                        result(FlutterError(code: "INIT_FAILED", message: "Handler creation failed", details: nil))
                    }
                }
            } else {
                result(FlutterMethodNotImplemented)
            }
        }

        // NSLog("✅ Initialization channel configured: panabit_client/init") // Debug NSLog commented for production
    }

    /// 登录时调用，创建并配置 PlatformChannelHandler
    public func setupPlatformChannelHandler() -> PlatformChannelHandler? {
        // NSLog("🛠️ Setting up PlatformChannelHandler...") // Debug NSLog commented for production

        guard Thread.isMainThread else {
            var result: PlatformChannelHandler?
            DispatchQueue.main.sync {
                result = self.setupPlatformChannelHandler()
            }
            return result
        }

        guard let controller = findFlutterViewController() else {
            // NSLog("❌ FlutterViewController not found during login setup") // Debug NSLog commented for production
            return nil
        }

        let messenger = controller.engine.binaryMessenger

        let handler = PlatformChannelHandler()
        handler.configureFlutter(binaryMessenger: messenger)
        platformChannelHandler = handler

        // NSLog("✅ PlatformChannelHandler configured") // Debug NSLog commented for production
        return handler
    }

    /// 查找 FlutterViewController，支持多Scene环境
    private func findFlutterViewController() -> FlutterViewController? {
        if let vc = window?.rootViewController as? FlutterViewController {
            return vc
        }

        for scene in UIApplication.shared.connectedScenes {
            if let windowScene = scene as? UIWindowScene {
                for win in windowScene.windows {
                    if let vc = win.rootViewController as? FlutterViewController {
                        return vc
                    }
                }
            }
        }
        return nil
    }

    /// 强制锁定竖屏方向
    public override func application(
        _ application: UIApplication,
        supportedInterfaceOrientationsFor window: UIWindow?
    ) -> UIInterfaceOrientationMask {
        return .portrait
    }
}