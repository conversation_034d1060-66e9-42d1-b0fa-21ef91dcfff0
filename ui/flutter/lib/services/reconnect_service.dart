/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      reconnect_service.dart
///
/// DESCRIPTION :    重连管理服务，负责处理网络连接重连流程，包括断开连接、
///                  路由设置和重新连接的完整流程管理
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:async';

import 'log_service.dart';
import 'connection_manager.dart';
import '../core/app_state.dart';
import '../models/server.dart';

/// ReconnectService
///
/// PURPOSE:
///     重连管理服务，负责处理网络连接重连流程，复用现有的ConnectionManager逻辑
///
/// FEATURES:
///     - 统一重连流程：disconnect -> 路由设置 -> connect
///     - 重连状态管理：跟踪重连进度和状态
///     - 错误处理：处理重连过程中的各种错误情况
///     - 日志记录：详细记录重连过程的每个步骤
///     - 复用现有逻辑：使用ConnectionManager的connect/disconnect方法
///
/// USAGE:
///     通过依赖注入获取实例，调用handleReconnectRequired处理重连需求
class ReconnectService {
  /// 连接管理器实例，用于执行连接操作
  final ConnectionManager connectionManager;

  /// 日志服务实例，用于记录重连过程
  final LogService logService;

  /// 应用状态实例，用于获取当前连接状态
  final AppState appState;

  /// 重连状态标志
  bool _isReconnecting = false;

  /// 当前重连的服务器信息
  Server? _lastConnectedServer;

  /// 重连尝试次数
  int _reconnectAttempts = 0;

  /// 最大重连尝试次数
  static const int _maxReconnectAttempts = 3;

  /// ReconnectService构造函数
  ///
  /// DESCRIPTION:
  ///     创建重连服务实例
  ///
  /// PARAMETERS:
  ///     connectionManager - 连接管理器实例
  ///     logService - 日志服务实例
  ///     appState - 应用状态实例
  ReconnectService({
    required this.connectionManager,
    required this.logService,
    required this.appState,
  });

  /// handleReconnectRequired
  ///
  /// DESCRIPTION:
  ///     处理重连需求，执行完整的重连流程
  ///
  /// PARAMETERS:
  ///     reason - 重连原因
  ///     message - 详细消息
  ///     serverInfo - 可选的服务器信息，如果为null则使用最后连接的服务器
  ///
  /// RETURNS:
  ///     Future<bool> - 重连是否成功
  Future<bool> handleReconnectRequired(String reason, String message, {Server? serverInfo}) async {
    if (_isReconnecting) {
      logService.warning('ReconnectService', 'Reconnection already in progress, ignoring new request');
      return false;
    }

    _isReconnecting = true;
    _reconnectAttempts++;

    // 在开始重连流程前，保存当前选中的服务器信息
    if (serverInfo == null && appState.selectedServer != null) {
      _lastConnectedServer = appState.selectedServer;
      logService.info('ReconnectService', 'Saved current selected server for reconnection: ${_lastConnectedServer!.name}');
    }

    try {
      logService.info('ReconnectService', '=== STARTING RECONNECTION PROCESS ===');
      logService.info('ReconnectService', 'Reason: $reason');
      logService.info('ReconnectService', 'Message: $message');
      logService.info('ReconnectService', 'Attempt: $_reconnectAttempts/$_maxReconnectAttempts');
      logService.info('ReconnectService', '=====================================');

      // 步骤1: 断开当前连接
      final disconnectSuccess = await _performDisconnect();
      if (!disconnectSuccess) {
        logService.warning('ReconnectService', 'Disconnect failed, but continuing with reconnection');
      }

      // 步骤2: 等待网络稳定
      await _waitForNetworkStability();

      // 步骤3: 设置路由（如果需要）
      await _setupRouting();

      // 步骤4: 重新连接
      final reconnectSuccess = await _performReconnect(serverInfo);

      if (reconnectSuccess) {
        logService.info('ReconnectService', 'Reconnection completed successfully');
        _reconnectAttempts = 0; // 重置重连计数
        return true;
      } else {
        logService.error('ReconnectService', 'Reconnection failed');
        
        // 如果还有重连机会，立即重试（不需要间隔）
        if (_reconnectAttempts < _maxReconnectAttempts) {
          logService.info('ReconnectService', 'Scheduling immediate retry (attempt ${_reconnectAttempts + 1}/$_maxReconnectAttempts)');
          // 立即重试，不需要延迟
          Future.microtask(() {
            handleReconnectRequired(reason, 'Retry after failed attempt', serverInfo: serverInfo);
          });
        } else {
          logService.error('ReconnectService', 'Maximum reconnection attempts reached, giving up');
          _reconnectAttempts = 0; // 重置计数器
        }
        
        return false;
      }

    } catch (e) {
      logService.error('ReconnectService', 'Unexpected error during reconnection: $e');
      return false;
    } finally {
      _isReconnecting = false;
    }
  }

  /// _performDisconnect
  ///
  /// DESCRIPTION:
  ///     执行断开连接操作
  ///
  /// RETURNS:
  ///     Future<bool> - 断开是否成功
  Future<bool> _performDisconnect() async {
    try {
      logService.info('ReconnectService', 'Step 1: Disconnecting current connection...');
      final success = await connectionManager.disconnect();
      if (success) {
        logService.info('ReconnectService', 'Step 1 completed: Disconnect successful');
        return true;
      } else {
        logService.warning('ReconnectService', 'Step 1 warning: Disconnect returned false');
        return false;
      }
    } catch (e) {
      logService.error('ReconnectService', 'Step 1 failed: Disconnect error - $e');
      return false;
    }
  }

  /// _waitForNetworkStability
  ///
  /// DESCRIPTION:
  ///     等待网络稳定，给网络接口变更一些时间完成
  ///
  /// RETURNS:
  ///     Future<void>
  Future<void> _waitForNetworkStability() async {
    logService.info('ReconnectService', 'Step 2: Waiting for network stability...');
    await Future.delayed(const Duration(seconds: 3));
    logService.info('ReconnectService', 'Step 2 completed: Network stability wait finished');
  }

  /// _setupRouting
  ///
  /// DESCRIPTION:
  ///     设置路由配置（如果需要）
  ///
  /// RETURNS:
  ///     Future<void>
  Future<void> _setupRouting() async {
    logService.info('ReconnectService', 'Step 3: Setting up routing...');
    // 这里可以添加具体的路由设置逻辑
    // 目前只是占位符，实际实现可能需要根据具体需求调整
    logService.info('ReconnectService', 'Step 3 completed: Routing setup finished');
  }

  /// _performReconnect
  ///
  /// DESCRIPTION:
  ///     执行重新连接操作
  ///
  /// PARAMETERS:
  ///     serverInfo - 可选的服务器信息
  ///
  /// RETURNS:
  ///     Future<bool> - 重连是否成功
  Future<bool> _performReconnect(Server? serverInfo) async {
    try {
      logService.info('ReconnectService', 'Step 4: Attempting to reconnect...');

      // 使用提供的服务器信息、最后连接的服务器或当前选中的服务器
      final targetServer = serverInfo ?? _lastConnectedServer ?? appState.selectedServer;

      if (targetServer == null) {
        logService.error('ReconnectService', 'Step 4 failed: No server information available for reconnection');
        logService.error('ReconnectService', 'Debug: serverInfo=$serverInfo, _lastConnectedServer=$_lastConnectedServer, appState.selectedServer=${appState.selectedServer}');
        return false;
      }

      // 确保目标服务器被选中
      appState.selectServer(targetServer, source: ServerSelectionSource.systemDefault);
      logService.info('ReconnectService', 'Selected server for reconnection: ${targetServer.name}');

      // 执行连接
      final success = await connectionManager.connect();

      if (success) {
        logService.info('ReconnectService', 'Step 4 completed: Reconnection successful to server ${targetServer.name}');
        // 保存成功连接的服务器信息，用于下次重连
        _lastConnectedServer = targetServer;
        return true;
      } else {
        logService.error('ReconnectService', 'Step 4 failed: Connection returned false');
        return false;
      }

    } catch (e) {
      logService.error('ReconnectService', 'Step 4 failed: Reconnection error - $e');
      return false;
    }
  }

  /// setLastConnectedServer
  ///
  /// DESCRIPTION:
  ///     设置最后连接的服务器信息，用于重连时使用
  ///
  /// PARAMETERS:
  ///     server - 服务器信息
  ///
  /// RETURNS:
  ///     void
  void setLastConnectedServer(Server server) {
    _lastConnectedServer = server;
    logService.debug('ReconnectService', 'Last connected server updated: ${server.name}');
  }

  /// isReconnecting
  ///
  /// DESCRIPTION:
  ///     获取当前是否正在重连
  ///
  /// RETURNS:
  ///     bool - 是否正在重连
  bool get isReconnecting => _isReconnecting;

  /// reconnectAttempts
  ///
  /// DESCRIPTION:
  ///     获取当前重连尝试次数
  ///
  /// RETURNS:
  ///     int - 重连尝试次数
  int get reconnectAttempts => _reconnectAttempts;
}
