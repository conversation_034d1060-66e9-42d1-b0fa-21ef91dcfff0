/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      app_state.dart
///
/// DESCRIPTION :    应用全局状态管理，负责连接状态、用户信息、服务器选择、
///                  流量统计等核心状态的统一管理和通知
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/server.dart';
import '../models/user_info.dart';
import '../models/traffic_stats.dart';
import '../models/interface_info.dart';
import '../utils/constants.dart';
import '../services/language_service.dart';
import '../services/log_service.dart';
import '../services/platform/cross_platform_storage_service.dart';
import 'dependency_injection.dart';

/// _AppStateLogger
///
/// PURPOSE:
///     AppState的日志助手类，提供统一的日志记录功能，减少重复代码
///
/// FEATURES:
///     - 安全日志记录：自动处理LogService获取失败的情况
///     - 服务器名称格式化：根据语言设置格式化服务器显示名称
///     - 统一日志模块：使用统一的日志模块标识
///
/// USAGE:
///     final logger = _AppStateLogger();
///     logger.logInfo('Connection status changed');
class _AppStateLogger {
  /// logInfo
  ///
  /// DESCRIPTION:
  ///     记录信息级别日志，自动处理LogService获取失败的情况
  ///
  /// PARAMETERS:
  ///     message - 日志消息
  ///
  /// RETURNS:
  ///     void
  static void logInfo(String message) {
    _safeLog((logService) => logService.info(LogModules.appState, message));
  }



  /// logWarning
  ///
  /// DESCRIPTION:
  ///     记录警告级别日志，自动处理LogService获取失败的情况
  ///
  /// PARAMETERS:
  ///     message - 日志消息
  ///
  /// RETURNS:
  ///     void
  static void logWarning(String message) {
    _safeLog((logService) => logService.warning(LogModules.appState, message));
  }

  /// getServerDisplayName
  ///
  /// DESCRIPTION:
  ///     获取服务器的显示名称，根据当前语言设置选择合适的名称
  ///
  /// PARAMETERS:
  ///     server - 服务器对象
  ///
  /// RETURNS:
  ///     String - 服务器显示名称
  static String getServerDisplayName(Server server) {
    try {
      final languageService = serviceLocator<LanguageService>();
      return server.getDisplayNameByLocale(languageService.isEnglish);
    } catch (e) {
      return server.name; // 降级到默认名称
    }
  }

  /// _safeLog
  ///
  /// DESCRIPTION:
  ///     安全地执行日志记录操作，自动处理LogService获取失败的情况
  ///
  /// PARAMETERS:
  ///     logAction - 日志记录操作函数
  ///
  /// RETURNS:
  ///     void
  static void _safeLog(void Function(LogService) logAction) {
    try {
      final logService = serviceLocator<LogService>();
      logAction(logService);
    } catch (e) {
      // 忽略LogService错误，确保应用正常运行
    }
  }
}

/// ServerSelectionSource
///
/// PURPOSE:
///     定义服务器选择的来源类型，用于区分不同的服务器选择场景
///
/// FEATURES:
///     - userSelection: 用户主动选择服务器
///     - backendPush: 后端状态推送选择服务器
///     - systemDefault: 系统默认选择服务器
///
/// USAGE:
///     用于AppState中跟踪服务器选择的来源，实现智能的服务器选择逻辑
enum ServerSelectionSource {
  userSelection,    // 用户主动选择
  backendPush,     // 后端状态推送
  systemDefault,   // 系统默认选择
}

/// AppState
///
/// PURPOSE:
///     应用全局状态管理，负责连接状态、用户信息、服务器选择、流量统计等核心状态的统一管理
///
/// FEATURES:
///     - 连接状态管理：跟踪VPN连接状态和消息
///     - 服务器管理：管理当前选中的服务器和选择来源
///     - 用户信息管理：管理用户认证状态和个人信息
///     - 流量统计：管理实时流量数据
///     - UI状态：管理加载状态和错误信息
///     - WebSocket状态：管理实时连接状态
///
/// USAGE:
///     通过Provider在整个应用中共享状态，各组件通过Consumer监听状态变化
class AppState extends ChangeNotifier {
  // 连接状态
  ConnectionStatus _connectionStatus = ConnectionStatus.disconnected;
  String _connectionMessage = '';
  Server? _selectedServer; // 当前选中/连接的服务器（统一管理）
  ServerSelectionSource _lastServerSelectionSource = ServerSelectionSource.systemDefault;
  DateTime? _connectedTime;

  // 用户信息
  UserInfo _userInfo = UserInfo();
  bool _isAuthenticated = false;

  // 网络状态
  TrafficStats _trafficStats = TrafficStats.empty();
  InterfaceInfo _interfaceInfo = InterfaceInfo.empty();
  InterfaceInfo? _cachedInterfaceInfo; // 缓存的接口信息

  // UI状态
  bool _isLoading = false;
  String? _errorMessage;

  // WebSocket状态
  bool _isWebSocketConnected = false;

  // 存储服务
  CrossPlatformStorageService? _storageService;
  bool _isStorageInitialized = false;

  // Getters
  ConnectionStatus get connectionStatus => _connectionStatus;
  String get connectionMessage => _connectionMessage;
  Server? get selectedServer => _selectedServer;
  DateTime? get connectedTime => _connectedTime;
  UserInfo get userInfo => _userInfo;
  bool get isAuthenticated => _isAuthenticated;
  TrafficStats get trafficStats => _trafficStats;

  /// interfaceInfo getter with caching logic
  ///
  /// DESCRIPTION:
  ///     返回接口信息，优先使用缓存的有效接口信息，避免显示"unknown"或空值
  InterfaceInfo get interfaceInfo {
    // 如果当前接口信息有有效的数据，直接返回
    if (_hasValidInterfaceInfo(_interfaceInfo)) {
      return _interfaceInfo;
    }

    // 如果当前接口信息无效，但有缓存的有效信息，返回缓存信息
    if (_cachedInterfaceInfo != null && _hasValidInterfaceInfo(_cachedInterfaceInfo!)) {
      return _cachedInterfaceInfo!;
    }

    // 都无效时返回当前信息（可能为空）
    return _interfaceInfo;
  }

  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isWebSocketConnected => _isWebSocketConnected;

  /// updateConnectionStatus
  ///
  /// DESCRIPTION:
  ///     更新VPN连接状态，支持后端推送的服务器信息和连接时间管理
  ///
  /// PARAMETERS:
  ///     status - 新的连接状态
  ///     message - 可选的状态消息
  ///     server - 可选的服务器信息（来自后端推送）
  ///     connectedTime - 可选的连接时间
  ///
  /// RETURNS:
  ///     void
  void updateConnectionStatus(
    ConnectionStatus status, {
    String? message,
    Server? server,
    DateTime? connectedTime,
  }) {
    final oldStatus = _connectionStatus;

    // 验证状态变更的合法性
    if (!_isValidStatusTransition(oldStatus, status, message)) {
      return;
    }

    // 更新连接状态和消息
    _connectionStatus = status;
    if (message != null) _connectionMessage = message;

    // 处理连接时间
    _updateConnectionTime(status, connectedTime);

    // 处理服务器信息
    if (server != null) {
      _handleServerFromBackend(server, status);
    }

    // 记录状态变化
    if (oldStatus != status) {
      _AppStateLogger.logInfo('Connection status changed: $oldStatus -> $status, message: ${message ?? _connectionMessage}');
    }

    notifyListeners();
  }

  /// _isValidStatusTransition
  ///
  /// DESCRIPTION:
  ///     验证连接状态转换是否合法，阻止明显的初始状态推送
  ///
  /// PARAMETERS:
  ///     oldStatus - 旧的连接状态
  ///     newStatus - 新的连接状态
  ///     message - 状态消息
  ///
  /// RETURNS:
  ///     bool - 状态转换是否合法
  bool _isValidStatusTransition(ConnectionStatus oldStatus, ConnectionStatus newStatus, String? message) {
    // 简化的状态变更验证：只阻止明显的初始状态推送
    if (oldStatus == ConnectionStatus.connecting && newStatus == ConnectionStatus.disconnected) {
      // 如果消息是简单的 "disconnected" 且没有其他信息，可能是登录时的初始状态推送
      // 但允许包含错误信息的disconnected状态转换（用于错误处理）
      if ((message == StatusMessages.disconnected || message == StatusMessages.disconnectedCapital) &&
          !message!.contains('error') && !message.contains('failed') && !message.contains('timeout')) {
        _AppStateLogger.logWarning('Blocking status change from connecting to disconnected: message=$message (likely initial state push)');
        return false;
      }
    }

    // Always allow transitions to error state for proper error handling
    if (newStatus == ConnectionStatus.error) {
      return true;
    }

    return true;
  }

  /// _updateConnectionTime
  ///
  /// DESCRIPTION:
  ///     更新连接时间，连接时设置时间，断开时清除时间
  ///
  /// PARAMETERS:
  ///     status - 连接状态
  ///     connectedTime - 连接时间
  ///
  /// RETURNS:
  ///     void
  void _updateConnectionTime(ConnectionStatus status, DateTime? connectedTime) {
    if (status == ConnectionStatus.connected && connectedTime != null) {
      _connectedTime = connectedTime;
      // _AppStateLogger.logDebug('Set connection time: ${connectedTime.toString()}');
    } else if (status == ConnectionStatus.disconnected) {
      _connectedTime = null;
      // _AppStateLogger.logDebug('Clear connection time (disconnected)');
    }
  }

  /// _handleServerFromBackend
  ///
  /// DESCRIPTION:
  ///     处理后端推送的服务器信息，智能合并服务器数据
  ///
  /// PARAMETERS:
  ///     backendServer - 后端推送的服务器信息
  ///     status - 当前连接状态
  ///
  /// RETURNS:
  ///     void
  void _handleServerFromBackend(Server backendServer, ConnectionStatus status) {
    // 情况1: 登录时的自动连接 - 后端选择的最佳服务器
    if (_shouldAcceptBackendServer(backendServer, status)) {
      // _AppStateLogger.logDebug('Accept backend selected server: $serverDisplayName (login auto-connect)');
      _selectedServer = backendServer;
      return;
    }

    // 情况2: 相同服务器的信息更新 - 合并最新数据
    if (_isSameServer(backendServer)) {
      // _AppStateLogger.logDebug('Update current server info: $serverDisplayName');
      _selectedServer = backendServer;
      return;
    }

    // 情况3: 不同服务器 - 保持用户选择，记录日志
    _logServerSelectionConflict(backendServer);
  }

  /// _shouldAcceptBackendServer
  ///
  /// DESCRIPTION:
  ///     判断是否应该接受后端推送的服务器
  ///
  /// PARAMETERS:
  ///     backendServer - 后端推送的服务器
  ///     status - 当前连接状态
  ///
  /// RETURNS:
  ///     bool - 是否应该接受后端服务器
  bool _shouldAcceptBackendServer(Server backendServer, ConnectionStatus status) {
    return _selectedServer == null ||
        (status == ConnectionStatus.connecting && _selectedServer!.id != backendServer.id);
  }

  /// _isSameServer
  ///
  /// DESCRIPTION:
  ///     判断是否是相同的服务器
  ///
  /// PARAMETERS:
  ///     server - 要比较的服务器
  ///
  /// RETURNS:
  ///     bool - 是否是相同服务器
  bool _isSameServer(Server server) {
    return _selectedServer != null && _selectedServer!.id == server.id;
  }

  /// _logServerSelectionConflict
  ///
  /// DESCRIPTION:
  ///     记录服务器选择冲突的日志
  ///
  /// PARAMETERS:
  ///     backendServer - 后端推送的服务器
  ///
  /// RETURNS:
  ///     void
  void _logServerSelectionConflict(Server backendServer) {
    if (_selectedServer != null) {
      // Debug logging removed for production
    }
  }

  /// selectServer
  ///
  /// DESCRIPTION:
  ///     选择服务器，支持来源标识和智能选择逻辑
  ///
  /// PARAMETERS:
  ///     server - 要选择的服务器
  ///     source - 服务器选择来源
  ///
  /// RETURNS:
  ///     void
  void selectServer(Server server, {ServerSelectionSource source = ServerSelectionSource.userSelection}) {
    final oldServer = _selectedServer;

    // 检查是否应该忽略后端推送
    if (_shouldIgnoreBackendPush(server, source)) {
      return;
    }

    // 更新服务器选择
    _selectedServer = server;
    _lastServerSelectionSource = source;

    // 记录服务器选择日志
    _logServerSelection(oldServer, server, source);

    notifyListeners();
  }

  /// _shouldIgnoreBackendPush
  ///
  /// DESCRIPTION:
  ///     判断是否应该忽略后端推送的服务器选择
  ///
  /// PARAMETERS:
  ///     server - 后端推送的服务器
  ///     source - 服务器选择来源
  ///
  /// RETURNS:
  ///     bool - 是否应该忽略后端推送
  bool _shouldIgnoreBackendPush(Server server, ServerSelectionSource source) {
    // 只有在特定条件下才允许后端覆盖用户选择
    if (source == ServerSelectionSource.backendPush &&
        _lastServerSelectionSource == ServerSelectionSource.userSelection) {
      // 检查是否是同一个服务器的信息更新
      if (_selectedServer?.id != server.id) {
        // 不同服务器，忽略后端推送
        // Debug logging removed for production
        return true;
      }
    }
    return false;
  }

  /// _logServerSelection
  ///
  /// DESCRIPTION:
  ///     记录服务器选择的日志
  ///
  /// PARAMETERS:
  ///     oldServer - 之前选择的服务器
  ///     newServer - 新选择的服务器
  ///     source - 服务器选择来源
  ///
  /// RETURNS:
  ///     void
  void _logServerSelection(Server? oldServer, Server newServer, ServerSelectionSource source) {
    final serverDisplayName = _AppStateLogger.getServerDisplayName(newServer);

    // 如果是同一个服务器但ping值不同，记录日志
    if (oldServer != null && oldServer.id == newServer.id && oldServer.ping != newServer.ping) {
      // _AppStateLogger.logDebug('Update server $serverDisplayName ping: ${oldServer.ping}ms -> ${newServer.ping}ms (source: $source)');
    } else if (oldServer?.id != newServer.id) {
      _AppStateLogger.logInfo('Server selected: $serverDisplayName (source: $source)');
    }
  }



  /// updateServerInfo
  ///
  /// DESCRIPTION:
  ///     更新服务器信息，不改变选择来源
  ///
  /// PARAMETERS:
  ///     server - 更新的服务器信息
  ///
  /// RETURNS:
  ///     void
  void updateServerInfo(Server server) {
    if (_selectedServer?.id == server.id) {
      _selectedServer = server;
      // _AppStateLogger.logDebug('Update server info: $serverDisplayName');
      notifyListeners();
    }
  }

  /// syncCurrentServerPing
  ///
  /// DESCRIPTION:
  ///     同步当前服务器的ping值和状态，来自WebSocket ping结果
  ///
  /// PARAMETERS:
  ///     newPing - 新的ping值
  ///     newStatus - 新的服务器状态
  ///
  /// RETURNS:
  ///     void
  void syncCurrentServerPing(int newPing, String newStatus) {
    if (_selectedServer != null &&
        (_selectedServer!.ping != newPing || _selectedServer!.status != newStatus)) {
      // _AppStateLogger.logDebug('Sync current server status: $serverDisplayName - ping: ${_selectedServer!.ping}ms -> ${newPing}ms, status: ${_selectedServer!.status} -> $newStatus');

      _selectedServer = Server(
        id: _selectedServer!.id,
        name: _selectedServer!.name,
        nameEn: _selectedServer!.nameEn,
        serverName: _selectedServer!.serverName,
        serverPort: _selectedServer!.serverPort,
        ping: newPing,
        isAuto: _selectedServer!.isAuto,
        status: newStatus,
      );
      notifyListeners();
    }
  }

  /// updateUserInfo
  ///
  /// DESCRIPTION:
  ///     更新用户信息
  ///
  /// PARAMETERS:
  ///     userInfo - 新的用户信息
  ///
  /// RETURNS:
  ///     void
  void updateUserInfo(UserInfo userInfo) {
    _userInfo = userInfo;
    notifyListeners();
  }

  /// updateAuthenticationStatus
  ///
  /// DESCRIPTION:
  ///     更新认证状态，未认证时清理相关状态
  ///
  /// PARAMETERS:
  ///     isAuthenticated - 是否已认证
  ///
  /// RETURNS:
  ///     void
  void updateAuthenticationStatus(bool isAuthenticated) {
    _isAuthenticated = isAuthenticated;
    if (!isAuthenticated) {
      _clearAuthenticationRelatedState();
    }
    notifyListeners();
  }

  /// _clearAuthenticationRelatedState
  ///
  /// DESCRIPTION:
  ///     清理认证相关的状态信息
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _clearAuthenticationRelatedState() {
    _userInfo = UserInfo();
    // 只有在非连接中状态时才能直接设置为未连接
    if (_connectionStatus != ConnectionStatus.connecting) {
      _connectionStatus = ConnectionStatus.disconnected;
    }
    _selectedServer = null;
    _lastServerSelectionSource = ServerSelectionSource.systemDefault;
    _connectedTime = null;
  }

  /// updateTrafficStats
  ///
  /// DESCRIPTION:
  ///     更新流量统计信息
  ///
  /// PARAMETERS:
  ///     stats - 新的流量统计数据
  ///
  /// RETURNS:
  ///     void
  void updateTrafficStats(TrafficStats stats) {
    _trafficStats = stats;
    notifyListeners();
  }

  /// updateInterfaceInfo
  ///
  /// DESCRIPTION:
  ///     更新网络接口信息，如果包含有效IP地址则缓存
  ///
  /// PARAMETERS:
  ///     info - 新的接口信息
  ///
  /// RETURNS:
  ///     void
  void updateInterfaceInfo(InterfaceInfo info) {
    _interfaceInfo = info;

    // 如果新的接口信息包含有效的数据，缓存它
    if (_hasValidInterfaceInfo(info)) {
      _saveCachedInterfaceInfo(info);
    }

    notifyListeners();
  }

  /// updateLoadingStatus
  ///
  /// DESCRIPTION:
  ///     更新加载状态
  ///
  /// PARAMETERS:
  ///     isLoading - 是否正在加载
  ///
  /// RETURNS:
  ///     void
  void updateLoadingStatus(bool isLoading) {
    _isLoading = isLoading;
    notifyListeners();
  }

  /// updateErrorMessage
  ///
  /// DESCRIPTION:
  ///     更新错误信息
  ///
  /// PARAMETERS:
  ///     message - 错误消息，null表示清除错误
  ///
  /// RETURNS:
  ///     void
  void updateErrorMessage(String? message) {
    _errorMessage = message;
    notifyListeners();
  }

  /// updateWebSocketStatus
  ///
  /// DESCRIPTION:
  ///     更新WebSocket连接状态
  ///
  /// PARAMETERS:
  ///     isConnected - 是否已连接
  ///
  /// RETURNS:
  ///     void
  void updateWebSocketStatus(bool isConnected) {
    _isWebSocketConnected = isConnected;
    notifyListeners();
  }

  /// clearError
  ///
  /// DESCRIPTION:
  ///     清除错误信息
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// reset
  ///
  /// DESCRIPTION:
  ///     重置所有状态到初始值
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void reset() {
    _resetConnectionState();
    _resetUserState();
    _resetNetworkState();
    _resetUIState();
    notifyListeners();
  }

  /// _resetConnectionState
  ///
  /// DESCRIPTION:
  ///     重置连接相关状态
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _resetConnectionState() {
    // 只有在非连接中状态时才能直接设置为未连接
    if (_connectionStatus != ConnectionStatus.connecting) {
      _connectionStatus = ConnectionStatus.disconnected;
    }
    _connectionMessage = '';
    _selectedServer = null;
    _lastServerSelectionSource = ServerSelectionSource.systemDefault;
    _connectedTime = null;
    _isWebSocketConnected = false;
  }

  /// _resetUserState
  ///
  /// DESCRIPTION:
  ///     重置用户相关状态
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _resetUserState() {
    _userInfo = UserInfo();
    _isAuthenticated = false;
  }

  /// _resetNetworkState
  ///
  /// DESCRIPTION:
  ///     重置网络相关状态
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _resetNetworkState() {
    _trafficStats = TrafficStats.empty();
    _interfaceInfo = InterfaceInfo.empty();
  }

  /// _resetUIState
  ///
  /// DESCRIPTION:
  ///     重置UI相关状态
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _resetUIState() {
    _isLoading = false;
    _errorMessage = null;
  }

  /// initializeStorage
  ///
  /// DESCRIPTION:
  ///     初始化存储服务并加载缓存的接口信息
  ///
  /// PARAMETERS:
  ///     storageService - 存储服务实例
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> initializeStorage(CrossPlatformStorageService storageService) async {
    _storageService = storageService;
    _isStorageInitialized = true;

    // 延迟加载缓存，确保存储服务完全初始化
    try {
      await _loadCachedInterfaceInfo();
      // _AppStateLogger.logDebug('Cached interface info loaded successfully');
    } catch (e) {
      _AppStateLogger.logWarning('Failed to load cached interface info during initialization: $e');
    }
  }

  /// _isStorageServiceInitialized
  ///
  /// DESCRIPTION:
  ///     检查存储服务是否已经初始化
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     bool - 存储服务是否已初始化
  bool _isStorageServiceInitialized() {
    return _isStorageInitialized && _storageService != null;
  }

  /// _hasValidInterfaceInfo
  ///
  /// DESCRIPTION:
  ///     检查接口信息是否包含有效的数据（IP地址或接口名称）
  ///
  /// PARAMETERS:
  ///     info - 要检查的接口信息
  ///
  /// RETURNS:
  ///     bool - 是否包含有效的接口信息
  bool _hasValidInterfaceInfo(InterfaceInfo info) {
    // 检查是否有有效的IP地址
    final hasValidIp = (info.localIp != null &&
                       info.localIp!.isNotEmpty &&
                       info.localIp != 'unknown') ||
                      (info.tunIp != null &&
                       info.tunIp!.isNotEmpty &&
                       info.tunIp != 'unknown');

    // 检查是否有有效的接口名称
    final hasValidInterfaceName = info.interfaceName != null &&
                                 info.interfaceName!.isNotEmpty &&
                                 info.interfaceName != 'unknown';

    // 只要有任何一个有效信息就认为值得缓存
    return hasValidIp || hasValidInterfaceName;
  }

  /// _loadCachedInterfaceInfo
  ///
  /// DESCRIPTION:
  ///     从存储中加载缓存的接口信息
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _loadCachedInterfaceInfo() async {
    try {
      // 检查存储服务是否已初始化
      if (!_isStorageServiceInitialized()) {
        // _AppStateLogger.logDebug('Storage service not yet initialized, skipping cache load');
        return;
      }

      final cachedJson = await _storageService!.getString(StorageKeys.cachedInterfaceInfo);
      if (cachedJson != null) {
        final cachedData = Map<String, dynamic>.from(
          Map.castFrom(const JsonDecoder().convert(cachedJson)),
        );
        _cachedInterfaceInfo = InterfaceInfo.fromJson(cachedData);
        // _AppStateLogger.logDebug('Successfully loaded cached interface info');
      }
    } catch (e) {
      // 静默处理缓存加载错误
      _AppStateLogger.logWarning('Failed to load cached interface info: $e');
    }
  }

  /// _saveCachedInterfaceInfo
  ///
  /// DESCRIPTION:
  ///     保存接口信息到缓存
  ///
  /// PARAMETERS:
  ///     info - 要缓存的接口信息
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _saveCachedInterfaceInfo(InterfaceInfo info) async {
    try {
      final infoJson = const JsonEncoder().convert(info.toJson());
      await _storageService?.setString(StorageKeys.cachedInterfaceInfo, infoJson);
      _cachedInterfaceInfo = info;
    } catch (e) {
      // 静默处理缓存保存错误
      _AppStateLogger.logWarning('Failed to save cached interface info: $e');
    }
  }
}
