/**
 * FILE: ServerManagerImpl.kt
 *
 * DESCRIPTION:
 *     Server manager implementation for Android VPN.
 *     Manages server list, selection, health monitoring, and ping testing.
 *     Compatible with iOS ServerManager and Go backend server management.
 *
 * AUTHOR: wei
 * HISTORY: 11/07/2025 create
 */

package com.panabit.client.connection

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError
import com.panabit.client.vpn.ITforceVPNService

import com.panabit.client.connection.models.*
import com.panabit.client.connection.ServerListProvider
import com.panabit.client.network.UDPConnection
import com.panabit.client.protocol.SDWANProtocol
import com.panabit.client.protocol.models.EncryptionMethod
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.sync.withPermit
import java.net.DatagramSocket
import java.net.DatagramPacket
import java.net.InetAddress
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

/**
 * NAME: ServerManagerImpl
 *
 * DESCRIPTION:
 *     Concrete implementation of ServerManager interface.
 *     Thread-safe server manager using Kotlin coroutines and StateFlow.
 *     Provides compatibility with iOS ServerManager and Go backend functionality.
 *
 * PROPERTIES:
 *     context - Android context for system operations
 *     udpConnectionFactory - Factory for creating UDP connections
 *     sdwanProtocolFactory - Factory for creating SDWAN protocol instances
 *     logger - Logging manager for structured logging
 *     serviceScope - Coroutine scope for background operations
 *     configuration - Server manager configuration
 */
class ServerManagerImpl(
    private val context: android.content.Context,
    private val udpConnectionFactory: (String, Int) -> UDPConnection,
    private val sdwanProtocolFactory: (String, Int) -> SDWANProtocol,
    private val serviceScope: CoroutineScope,
    private val configuration: ServerConfiguration = ServerConfiguration.default
) : ServerManager {

    // State management
    private val _servers = MutableStateFlow<List<ServerInfo>>(emptyList())
    override val servers: StateFlow<List<ServerInfo>> = _servers.asStateFlow()

    private val _pingResults = MutableStateFlow<Map<String, Int>>(emptyMap())
    override val pingResults: StateFlow<Map<String, Int>> = _pingResults.asStateFlow()

    private val _currentServer = MutableStateFlow<ServerInfo?>(null)
    override val currentServer: StateFlow<ServerInfo?> = _currentServer.asStateFlow()

    private val _selectionMode = MutableStateFlow(ServerSelectionMode.Auto)
    override val selectionMode: StateFlow<ServerSelectionMode> = _selectionMode.asStateFlow()

    // Internal state
    private val isRunning = AtomicBoolean(false)
    private val serverMutex = Mutex()
    private val pingSemaphore = Semaphore(configuration.maxConcurrentPings)
    private val pingResultsCache = ConcurrentHashMap<String, PingResult>()

    // Background tasks
    private var updateTask: Job? = null
    private var pingTask: Job? = null

    // Server list provider
    private var serverListProvider: ServerListProvider? = null

    // Server list version tracking for UI update optimization
    private var lastServerListVersion: String? = null

    // Simple IP cache for DNS resolution (hostname -> IP)
    private val serverIPCache = ConcurrentHashMap<String, String>()

    // Callback management
    private val updateCallbacks = mutableSetOf<(List<ServerInfo>) -> Unit>()
    private val statusCallbacks = mutableSetOf<(String, ServerStatus) -> Unit>()
    private val callbackMutex = Mutex()

    init {
        logInfo("ServerManager initialized", mapOf(
            "ping_timeout" to configuration.pingTimeout,
            "ping_interval" to configuration.pingInterval,
            "max_concurrent_pings" to configuration.maxConcurrentPings
        ))
    }

    /**
     * NAME: start
     *
     * DESCRIPTION:
     *     Starts server manager background tasks.
     *     Begins server list updates, ping monitoring, and network monitoring.
     *
     * THROWS:
     *     ServerManagerError - If start operation fails
     */
    override suspend fun start() {
        if (isRunning.getAndSet(true)) {
            logWarn("Server manager already running")
            return
        }

        logInfo("Starting server manager", mapOf(
            "configuration" to mapOf(
                "ping_interval_ms" to configuration.pingInterval,
                "ping_timeout_ms" to configuration.pingTimeout,
                "update_interval_ms" to configuration.updateInterval,
                "max_concurrent_pings" to configuration.maxConcurrentPings
            )
        ))

        try {
            // Start background tasks
            logInfo("Starting background update task")
            startUpdateTask()

            logInfo("Starting background ping task")
            startPingTask()

            logInfo("Server manager started successfully - background tasks are now running")
        } catch (e: Exception) {
            isRunning.set(false)
            logError("Failed to start server manager: ${e.message}", e)
            throw ServerManagerError.UpdateFailed("Failed to start server manager: ${e.message}")
        }
    }

    /**
     * NAME: stop
     *
     * DESCRIPTION:
     *     Stops server manager background tasks and cleans up resources.
     */
    override suspend fun stop() {
        if (!isRunning.getAndSet(false)) {
            logWarn("Server manager already stopped")
            return
        }

        logInfo("Stopping server manager")

        try {
            // Cancel background tasks
            updateTask?.cancel()
            pingTask?.cancel()

            // Wait for tasks to complete
            updateTask?.join()
            pingTask?.join()

            // Clear state
            pingResultsCache.clear()

            logInfo("Server manager stopped successfully")
        } catch (e: Exception) {
            logError("Error stopping server manager: ${e.message}", e)
        }
    }

    /**
     * NAME: setServerListUrl
     *
     * DESCRIPTION:
     *     Sets the server list URL for remote fetching.
     *     Creates a new ServerListProvider with the specified URL.
     *
     * PARAMETERS:
     *     url - Server list endpoint URL
     *     skipSslVerification - Whether to skip SSL certificate verification
     */
    suspend fun setServerListUrl(url: String, skipSslVerification: Boolean = false) {
        logInfo("Setting server list URL", mapOf(
            "url" to url,
            "skip_ssl_verification" to skipSslVerification
        ))

        serverListProvider = ServerListProvider(
            baseUrl = url,
            timeout = configuration.pingTimeout * 2, // Use longer timeout for HTTP requests
            skipSslVerification = skipSslVerification
        )

        // Trigger immediate server list update if manager is running
        if (isRunning.get()) {
            try {
                fetchAndUpdateServerList()
            } catch (e: Exception) {
                logWarn("Failed to fetch server list after URL update: ${e.message}")
            }
        }
    }

    /**
     * NAME: fetchAndUpdateServerList
     *
     * DESCRIPTION:
     *     Fetches server list from remote provider and updates local list.
     *     Implements version checking to avoid unnecessary UI updates.
     *     Used by background update task and manual refresh operations.
     *
     * THROWS:
     *     ServerManagerError.UpdateFailed - If fetch operation fails
     */
    suspend fun fetchAndUpdateServerList() {
        val provider = serverListProvider
            ?: throw ServerManagerError.UpdateFailed("No server list provider configured")

        try {
            logDebug("Fetching server list from remote provider")
            val serverListWithVersion = provider.fetchServerList()

            if (serverListWithVersion.servers.isNotEmpty()) {
                // Check if version has changed to avoid unnecessary UI updates
                val currentVersion = serverListWithVersion.version
                val previousVersion = lastServerListVersion

                if (previousVersion != null && previousVersion == currentVersion) {
                    logInfo("Server list version unchanged, skipping UI update", mapOf(
                        "version" to currentVersion,
                        "server_count" to serverListWithVersion.servers.size
                    ))
                    return
                }

                // Version changed or first time, update server list but defer UI notification
                updateServerListSilently(serverListWithVersion.servers)
                lastServerListVersion = currentVersion

                logInfo("Server list updated from remote provider (UI update deferred to next ping)", mapOf(
                    "server_count" to serverListWithVersion.servers.size,
                    "version" to currentVersion,
                    "previous_version" to (previousVersion ?: "none"),
                    "version_changed" to (previousVersion != currentVersion)
                ))
            } else {
                logWarn("Remote provider returned empty server list")
            }
        } catch (e: Exception) {
            logError("Failed to fetch server list from remote provider: ${e.message}", e)
            throw ServerManagerError.UpdateFailed("Failed to fetch server list: ${e.message}")
        }
    }

    /**
     * NAME: updateServerList
     *
     * DESCRIPTION:
     *     Updates server list with new server information.
     *     Triggers server list StateFlow updates for observers.
     *
     * PARAMETERS:
     *     newServers - List of updated server information
     *
     * THROWS:
     *     ServerManagerError.UpdateFailed - If update operation fails
     */
    override suspend fun updateServerList(newServers: List<ServerInfo>) {
        if (newServers.isEmpty()) {
            throw ServerManagerError.UpdateFailed("Empty server list provided")
        }

        logInfo("Updating server list - new_count: ${newServers.size}, old_count: ${_servers.value.size}")

        try {
            serverMutex.withLock {
                // Validate servers
                val validServers = newServers.filter { it.isValid() }
                if (validServers.size != newServers.size) {
                    logWarn("Some servers failed validation - total: ${newServers.size}, valid: ${validServers.size}")
                }

                // Update server list
                _servers.value = validServers

                // Reset ping results for new servers
                pingResultsCache.clear()
                _pingResults.value = emptyMap()

                // Notify callbacks
                notifyUpdateCallbacks(validServers)

                logInfo("Server list updated successfully - server_count: ${validServers.size}")
            }

            // Pre-resolve all server IPs in background to avoid DNS failures during connection/ping
            logInfo("Starting batch IP resolution for server list", mapOf("server_count" to newServers.size))
            serviceScope.launch {
                try {
                    var successCount = 0
                    newServers.forEach { server ->
                        try {
                            val resolvedIP = resolveHostnameToIP(server.serverName)
                            if (resolvedIP != null) {
                                serverIPCache[server.serverName] = resolvedIP
                                successCount++
                            }
                        } catch (e: Exception) {
                            logWarn("Failed to resolve server IP", mapOf(
                                "hostname" to server.serverName,
                                "error" to (e.message ?: "unknown")
                            ))
                        }
                    }
                    logInfo("Batch IP resolution completed", mapOf(
                        "total_servers" to newServers.size,
                        "successful_resolves" to successCount,
                        "failed_resolves" to (newServers.size - successCount)
                    ))
                } catch (e: Exception) {
                    logError("Batch IP resolution failed: ${e.message}", e)
                }
            }
        } catch (e: Exception) {
            logError("Failed to update server list: ${e.message}", e)
            throw ServerManagerError.UpdateFailed("Failed to update server list: ${e.message}")
        }
    }

    /**
     * NAME: updateServerListSilently
     *
     * DESCRIPTION:
     *     Updates server list with new server information without triggering UI notifications.
     *     Used when server list version changes but we want to defer UI updates until next ping.
     *
     * PARAMETERS:
     *     newServers - List of updated server information
     *
     * THROWS:
     *     ServerManagerError.UpdateFailed - If update operation fails
     */
    private suspend fun updateServerListSilently(newServers: List<ServerInfo>) {
        if (newServers.isEmpty()) {
            throw ServerManagerError.UpdateFailed("Empty server list provided")
        }

        logInfo("Updating server list silently - new_count: ${newServers.size}, old_count: ${_servers.value.size}")

        try {
            serverMutex.withLock {
                // Validate servers
                val validServers = newServers.filter { it.isValid() }
                if (validServers.size != newServers.size) {
                    logWarn("Some servers failed validation - total: ${newServers.size}, valid: ${validServers.size}")
                }

                // Update server list without triggering StateFlow observers
                _servers.value = validServers

                // Reset ping results for new servers
                pingResultsCache.clear()
                _pingResults.value = emptyMap()

                // Note: Do NOT call notifyUpdateCallbacks() to avoid immediate UI updates

                logInfo("Server list updated silently - server_count: ${validServers.size}")
            }

            // Pre-resolve all server IPs in background to avoid DNS failures during connection/ping
            logInfo("Starting batch IP resolution for server list", mapOf("server_count" to newServers.size))
            serviceScope.launch {
                try {
                    var successCount = 0
                    newServers.forEach { server ->
                        try {
                            val resolvedIP = resolveHostnameToIP(server.serverName)
                            if (resolvedIP != null) {
                                serverIPCache[server.serverName] = resolvedIP
                                successCount++
                            }
                        } catch (e: Exception) {
                            logWarn("Failed to resolve server IP", mapOf(
                                "hostname" to server.serverName,
                                "error" to (e.message ?: "unknown")
                            ))
                        }
                    }
                    logInfo("Batch IP resolution completed", mapOf(
                        "total_servers" to newServers.size,
                        "successful_resolves" to successCount,
                        "failed_resolves" to (newServers.size - successCount)
                    ))
                } catch (e: Exception) {
                    logError("Batch IP resolution failed: ${e.message}", e)
                }
            }
        } catch (e: Exception) {
            logError("Failed to update server list silently: ${e.message}", e)
            throw ServerManagerError.UpdateFailed("Failed to update server list: ${e.message}")
        }
    }

    /**
     * NAME: getServers
     *
     * DESCRIPTION:
     *     Returns current server list sorted by latency.
     *     Online servers are prioritized first, then sorted by ping time.
     *
     * RETURNS:
     *     List<ServerInfo> - Server list sorted by latency
     */
    override fun getServers(): List<ServerInfo> {
        return sortServersByLatency(_servers.value)
    }

    /**
     * NAME: selectBestServer
     *
     * DESCRIPTION:
     *     Selects best server based on current selection mode.
     *     Compatible with iOS selectBestServer() functionality.
     *
     * RETURNS:
     *     ServerInfo? - Best server or null if none available
     *
     * THROWS:
     *     ServerManagerError.NoServersAvailable - If no servers available
     */
    override suspend fun selectBestServer(): ServerInfo? {
        logInfo("Selecting best server - servers_count: ${_servers.value.size}, selection_mode: ${_selectionMode.value.name}")

        if (_servers.value.isEmpty()) {
            logError("No servers available for selection")
            throw ServerManagerError.NoServersAvailable
        }

        val selectedServer = when (_selectionMode.value) {
            ServerSelectionMode.Auto -> selectBestAutoServer()
            ServerSelectionMode.Manual -> _currentServer.value
        }

        selectedServer?.let { server ->
            logInfo("Best server selected - mode: ${_selectionMode.value.name}, server_id: ${server.id}, server_name: ${server.name}, ping: ${server.ping}, status: ${server.status.name}")
        }

        return selectedServer
    }

    /**
     * NAME: selectBestAutoServer
     *
     * DESCRIPTION:
     *     Selects best auto server (isAuto = true) based on latency.
     *     Compatible with iOS selectBestAutoServer() functionality.
     *
     * RETURNS:
     *     ServerInfo? - Best auto server or null if none available
     *
     * THROWS:
     *     ServerManagerError.NoServersAvailable - If no auto servers available
     */
    override suspend fun selectBestAutoServer(): ServerInfo? {
        logInfo("Selecting best auto server - total_servers: ${_servers.value.size}")

        val autoServers = _servers.value.filter { it.isAuto }
        if (autoServers.isEmpty()) {
            logInfo("No auto servers found")
            throw ServerManagerError.NoServersAvailable
        }

        // Sort by latency - prioritize online servers first, then sort by latency
        val sortedServers = sortServersByLatency(autoServers)
        val bestServer = sortedServers.first()

        logInfo("Selected best auto server - server_id: ${bestServer.id}, server_name: ${bestServer.name}, status: ${bestServer.status.name}, ping: ${bestServer.ping}")

        return bestServer
    }

    /**
     * NAME: pingAllServers
     *
     * DESCRIPTION:
     *     Performs ping test on all available servers concurrently.
     *     Updates ping results StateFlow with test results.
     *     Compatible with iOS pingAllServers() functionality.
     *
     * RETURNS:
     *     Map<String, Int> - Map of server IDs to ping latency (ms)
     */
    override suspend fun pingAllServers(): Map<String, Int> {
        val servers = _servers.value
        if (servers.isEmpty()) {
            println("🔍 [PING_ALL] No servers available for ping testing")
            logWarn("No servers available for ping testing")
            return emptyMap()
        }

        val startTime = System.currentTimeMillis()
        println("🔍 [PING_ALL_START] Starting ping test for ALL servers - server_count: ${servers.size}, start_time: $startTime, thread: ${Thread.currentThread().name}")

        logInfo("Starting ping test for all servers - server_count: ${servers.size}")

        val results = mutableMapOf<String, Int>()

        try {
            // Perform concurrent ping tests on IO dispatcher
            val concurrentStartTime = System.currentTimeMillis()
            println("🔍 [PING_ALL_CONCURRENT] Starting concurrent ping jobs for ${servers.size} servers - concurrent_start: $concurrentStartTime")

            val pingJobs = servers.map { server ->
                serviceScope.async(Dispatchers.IO) {
                    println("🔍 [PING_JOB_START] Starting ping job for server ${server.name} - thread: ${Thread.currentThread().name}")

                    pingSemaphore.withPermit {
                        val serverPingStart = System.currentTimeMillis()
                        val pingResult = performPing(server)
                        val serverPingTime = System.currentTimeMillis() - serverPingStart

                        println("🔍 [PING_JOB_RESULT] Ping job completed for server ${server.name} - result: ${pingResult.latency}ms, success: ${pingResult.isSuccess}, job_time: ${serverPingTime}ms")

                        // Update server ping result
                        serverMutex.withLock {
                            val serverIndex = _servers.value.indexOfFirst { it.id == server.id }
                            if (serverIndex >= 0) {
                                val updatedServers = _servers.value.toMutableList()
                                val oldPing = updatedServers[serverIndex].ping

                                println("🔍 [PING_UPDATE] Before update - Server ${server.name}: ping=${oldPing}ms, pingResult.isSuccess=${pingResult.isSuccess}, pingResult.latency=${pingResult.latency}ms")

                                updatedServers[serverIndex].updatePingResult(pingResult)
                                _servers.value = updatedServers

                                println("🔍 [PING_UPDATE] After update - Server ${server.name}: ping=${updatedServers[serverIndex].ping}ms (UI will show ${if (updatedServers[serverIndex].ping > 0) "reachable" else "unreachable"})")
                            } else {
                                println("🔍 [ERROR] Server ${server.name} not found in server list for ping update")
                            }
                        }

                        // Store result
                        pingResultsCache[server.id] = pingResult
                        server.id to pingResult.latency
                    }
                }
            }

            // Wait for all ping tests to complete
            val awaitStartTime = System.currentTimeMillis()
            println("🔍 [PING_ALL_AWAIT] Waiting for all ${pingJobs.size} ping jobs to complete - await_start: $awaitStartTime")

            val pingResults = pingJobs.awaitAll()
            results.putAll(pingResults)

            val awaitTime = System.currentTimeMillis() - awaitStartTime
            val totalConcurrentTime = System.currentTimeMillis() - concurrentStartTime
            println("🔍 [PING_ALL_AWAIT_COMPLETE] All ping jobs completed - await_time: ${awaitTime}ms, total_concurrent_time: ${totalConcurrentTime}ms, results: $pingResults")

            // Update ping results StateFlow
            val updateStart = System.currentTimeMillis()
            _pingResults.value = results
            val updateTime = System.currentTimeMillis() - updateStart

            val duration = System.currentTimeMillis() - startTime
            val successfulPings = results.values.count { it > 0 }

            println("🔍 [PING_ALL_COMPLETE] Ping test for ALL servers COMPLETED - duration: ${duration}ms, successful: $successfulPings/${servers.size}, update_time: ${updateTime}ms")

            logInfo("Ping test completed - duration_ms: $duration, successful_pings: $successfulPings, total_servers: ${servers.size}")

        } catch (e: Exception) {
            val errorTime = System.currentTimeMillis() - startTime
            println("🔍 [PING_ALL_ERROR] Ping test for ALL servers FAILED - error: ${e.message}, total_time: ${errorTime}ms, thread: ${Thread.currentThread().name}")

            logError("Ping test failed: ${e.message}", e)
            throw ServerManagerError.PingFailed("Ping test failed: ${e.message}")
        }

        return results
    }

    /**
     * NAME: pingServer
     *
     * DESCRIPTION:
     *     Performs ping test on specific server.
     *     Uses SDWAN protocol-based ping for accurate latency measurement.
     *
     * PARAMETERS:
     *     server - Server to ping test
     *
     * RETURNS:
     *     Int - Ping latency in milliseconds, -1 if failed
     */
    override suspend fun pingServer(server: ServerInfo): Int {
        logDebug("Pinging server - server_id: ${server.id}, server_name: ${server.name}")

        return try {
            val pingResult = performPing(server)

            // Update server status
            serverMutex.withLock {
                val serverIndex = _servers.value.indexOfFirst { it.id == server.id }
                if (serverIndex >= 0) {
                    val updatedServers = _servers.value.toMutableList()
                    val oldStatus = updatedServers[serverIndex].status
                    updatedServers[serverIndex].updatePingResult(pingResult)
                    _servers.value = updatedServers

                    // Notify status change if changed
                    if (oldStatus != updatedServers[serverIndex].status) {
                        notifyStatusCallbacks(server.id, updatedServers[serverIndex].status)
                    }
                }
            }

            // Store result
            pingResultsCache[server.id] = pingResult
            _pingResults.value = _pingResults.value + (server.id to pingResult.latency)

            if (pingResult.isSuccess) {
                logDebug("Server ping successful - server_id: ${server.id}, latency_ms: ${pingResult.latency}")
                pingResult.latency
            } else {
                logDebug("Server ping failed - server_id: ${server.id}, error: ${pingResult.error ?: "Unknown error"}")
                -1
            }
        } catch (e: Exception) {
            logError("Ping operation failed - server_id: ${server.id}, error: ${e.message}", e)
            -1
        }
    }

    /**
     * NAME: getServerById
     *
     * DESCRIPTION:
     *     Retrieves server information by server ID.
     *
     * PARAMETERS:
     *     serverId - Unique server identifier
     *
     * RETURNS:
     *     ServerInfo? - Server information or null if not found
     */
    override fun getServerById(serverId: String): ServerInfo? {
        return _servers.value.find { it.id == serverId }
    }

    /**
     * NAME: isServerAvailable
     *
     * DESCRIPTION:
     *     Checks if server is currently available for connections.
     *     Based on ping results only - ping > 0 means available.
     *
     * PARAMETERS:
     *     server - Server to check availability
     *
     * RETURNS:
     *     Boolean - true if server is available (ping > 0), false otherwise
     */
    override suspend fun isServerAvailable(server: ServerInfo): Boolean {
        val cachedResult = pingResultsCache[server.id]

        // If no cached result or result is too old, perform fresh ping
        if (cachedResult == null ||
            System.currentTimeMillis() - cachedResult.timestamp > configuration.pingInterval) {
            val pingLatency = pingServer(server)
            return pingLatency > 0
        }

        // Use ping value to determine availability - consistent with UI logic
        return cachedResult.isSuccess && cachedResult.latency > 0
    }

    /**
     * NAME: setSelectionMode
     *
     * DESCRIPTION:
     *     Sets server selection mode (Auto/Manual).
     *
     * PARAMETERS:
     *     mode - Server selection mode
     */
    override fun setSelectionMode(mode: ServerSelectionMode) {
        logInfo("Setting selection mode: ${mode.name}")
        _selectionMode.value = mode
    }

    /**
     * NAME: setCurrentServer
     *
     * DESCRIPTION:
     *     Sets current manually selected server.
     *
     * PARAMETERS:
     *     server - Server to set as current (null to clear)
     */
    override fun setCurrentServer(server: ServerInfo?) {
        logInfo("Setting current server - server_id: ${server?.id ?: "null"}, server_name: ${server?.name ?: "null"}")
        _currentServer.value = server
    }

    /**
     * NAME: addServerUpdateCallback
     *
     * DESCRIPTION:
     *     Adds callback for server list updates.
     *
     * PARAMETERS:
     *     callback - Callback function to add
     */
    override fun addServerUpdateCallback(callback: (List<ServerInfo>) -> Unit) {
        serviceScope.launch {
            callbackMutex.withLock {
                updateCallbacks.add(callback)
            }
        }
    }

    /**
     * NAME: addStatusChangeCallback
     *
     * DESCRIPTION:
     *     Adds callback for server status changes.
     *
     * PARAMETERS:
     *     callback - Callback function to add
     */
    override fun addStatusChangeCallback(callback: (String, ServerStatus) -> Unit) {
        serviceScope.launch {
            callbackMutex.withLock {
                statusCallbacks.add(callback)
            }
        }
    }

    /**
     * NAME: removeServerUpdateCallback
     *
     * DESCRIPTION:
     *     Removes callback for server list updates.
     *
     * PARAMETERS:
     *     callback - Callback function to remove
     */
    override fun removeServerUpdateCallback(callback: (List<ServerInfo>) -> Unit) {
        serviceScope.launch {
            callbackMutex.withLock {
                updateCallbacks.remove(callback)
            }
        }
    }

    /**
     * NAME: removeStatusChangeCallback
     *
     * DESCRIPTION:
     *     Removes callback for server status changes.
     *
     * PARAMETERS:
     *     callback - Callback function to remove
     */
    override fun removeStatusChangeCallback(callback: (String, ServerStatus) -> Unit) {
        serviceScope.launch {
            callbackMutex.withLock {
                statusCallbacks.remove(callback)
            }
        }
    }

    // MARK: - Private Helper Methods

    /**
     * NAME: sortServersByLatency
     *
     * DESCRIPTION:
     *     Sorts servers by latency - prioritize online servers first, then sort by latency.
     *     Compatible with iOS/Windows server sorting algorithm.
     *
     * PARAMETERS:
     *     servers - List of servers to sort
     *
     * RETURNS:
     *     List<ServerInfo> - Sorted server list
     */
    private fun sortServersByLatency(servers: List<ServerInfo>): List<ServerInfo> {
        return servers.sortedWith { server1, server2 ->
            // Prioritize online servers
            when {
                server1.status == ServerStatus.Online && server2.status != ServerStatus.Online -> -1
                server1.status != ServerStatus.Online && server2.status == ServerStatus.Online -> 1
                else -> {
                    // Status is the same, sort by latency
                    // Treat servers with 0 ping as high latency (9999)
                    val ping1 = if (server1.ping > 0) server1.ping else 9999
                    val ping2 = if (server2.ping > 0) server2.ping else 9999
                    ping1.compareTo(ping2)
                }
            }
        }
    }

    /**
     * NAME: performPing
     *
     * DESCRIPTION:
     *     Performs SDWAN protocol-based ping test on server using OPEN packet.
     *     Uses 3-packet concurrent testing for improved accuracy - sends 3 packets simultaneously
     *     and returns the lowest latency from successful responses.
     *     Compatible with iOS ping implementation - sends OPEN packet and waits for any response.
     *     Uses lightweight approach without full authentication flow.
     *
     * PARAMETERS:
     *     server - Server to ping
     *
     * RETURNS:
     *     PingResult - Ping test result
     */
    private suspend fun performPing(server: ServerInfo): PingResult = withContext(Dispatchers.IO) {
        val pingStartTime = System.currentTimeMillis()
        println("🔍 [PING_START] Starting 3-packet ping test for server ${server.name} - start_time: $pingStartTime, thread: ${Thread.currentThread().name}")

        return@withContext withTimeout(configuration.pingTimeout) { // Use same timeout as iOS for consistency
            try {
                logDebug("Starting 3-packet concurrent ping test for server ${server.name}", mapOf(
                    "server_address" to server.serverName,
                    "server_port" to server.serverPort,
                    "timeout_ms" to configuration.pingTimeout,
                    "thread" to Thread.currentThread().name,
                    "ping_start_time" to pingStartTime
                ))

                // Resolve server IP using cache first, fallback to direct resolution
                val serverIP = serverIPCache[server.serverName]
                    ?: resolveHostnameToIP(server.serverName)

                // Determine what to use for connection and protocol creation
                val (addressToUse, protocolAddress, useResolvedIP) = if (serverIP != null) {
                    // Cache the resolved IP if not already cached
                    if (!serverIPCache.containsKey(server.serverName)) {
                        serverIPCache[server.serverName] = serverIP
                    }

                    logDebug("Using resolved server IP for 3-packet ping test", mapOf(
                        "hostname" to server.serverName,
                        "resolved_ip" to serverIP,
                        "from_cache" to serverIPCache.containsKey(server.serverName)
                    ))

                    Triple(serverIP, serverIP, true)
                } else {
                    // IP resolution failed, use hostname as fallback
                    logWarn("IP resolution failed, using hostname as fallback for 3-packet ping test", mapOf(
                        "hostname" to server.serverName,
                        "note" to "Will perform DNS resolution in protocol layer"
                    ))

                    Triple(server.serverName, server.serverName, false)
                }

                // Execute 3 concurrent ping attempts
                val concurrentStart = System.currentTimeMillis()
                println("🔍 [CONCURRENT_START] Starting 3 concurrent ping attempts for server ${server.name} - concurrent_start: $concurrentStart")

                val results = (1..3).map { packetIndex ->
                    async {
                        println("🔍 [ASYNC_START] Starting async task for packet $packetIndex - server: ${server.name}, thread: ${Thread.currentThread().name}")
                        val result = performSinglePingAttempt(
                            server = server,
                            addressToUse = addressToUse,
                            protocolAddress = protocolAddress,
                            useResolvedIP = useResolvedIP,
                            packetIndex = packetIndex
                        )
                        println("🔍 [ASYNC_END] Async task completed for packet $packetIndex - server: ${server.name}, result: $result")
                        result
                    }
                }.awaitAll()

                val concurrentEnd = System.currentTimeMillis()
                val concurrentTime = concurrentEnd - concurrentStart
                println("🔍 [CONCURRENT_END] All 3 ping attempts completed for server ${server.name} - concurrent_time: ${concurrentTime}ms, results: $results")

                // Process results: filter successful pings and find minimum latency
                val resultProcessStart = System.currentTimeMillis()
                println("🔍 [RESULT_PROCESS] Processing ping results for server ${server.name} - raw_results: $results")

                val successfulLatencies = results.mapNotNull { (latency, success) ->
                    if (success && latency > 0) latency else null
                }

                val resultProcessTime = System.currentTimeMillis() - resultProcessStart
                val totalPingTime = System.currentTimeMillis() - pingStartTime
                println("🔍 [RESULT_PROCESS] Results processed for server ${server.name} - successful_latencies: $successfulLatencies, process_time: ${resultProcessTime}ms, total_ping_time: ${totalPingTime}ms")

                if (successfulLatencies.isNotEmpty()) {
                    val minLatency = successfulLatencies.minOrNull() ?: 0
                    println("🔍 [PING_SUCCESS] Ping test SUCCESSFUL for server ${server.name} - min_latency: ${minLatency}ms, successful_count: ${successfulLatencies.size}/3, total_time: ${totalPingTime}ms")

                    logDebug("3-packet ping test completed successfully", mapOf(
                        "server" to server.name,
                        "successful_packets" to "${successfulLatencies.size}/3",
                        "latencies" to successfulLatencies.joinToString(", ") { "${it}ms" },
                        "min_latency" to "${minLatency}ms",
                        "total_ping_time" to "${totalPingTime}ms",
                        "concurrent_time" to "${concurrentTime}ms"
                    ))

                    PingResult(
                        latency = minLatency,
                        isSuccess = true,
                        timestamp = System.currentTimeMillis()
                    )
                } else {
                    println("🔍 [PING_FAILED] Ping test FAILED for server ${server.name} - no successful responses, total_time: ${totalPingTime}ms")

                    logDebug("3-packet ping test failed - no successful responses", mapOf(
                        "server" to server.name,
                        "failed_packets" to "3/3",
                        "total_ping_time" to "${totalPingTime}ms",
                        "concurrent_time" to "${concurrentTime}ms"
                    ))

                    PingResult(
                        latency = 0,
                        isSuccess = false,
                        timestamp = System.currentTimeMillis(),
                        error = "No response from server"
                    )
                }



            } catch (e: TimeoutCancellationException) {
                val timeoutTime = System.currentTimeMillis()
                val totalTimeoutTime = timeoutTime - pingStartTime
                println("🔍 [PING_TIMEOUT] Ping test TIMEOUT for server ${server.name} - timeout_time: ${totalTimeoutTime}ms, configured_timeout: ${configuration.pingTimeout}ms")

                logWarn("3-packet ping test timeout for server ${server.name}", mapOf(
                    "timeout_ms" to configuration.pingTimeout,
                    "actual_time_ms" to totalTimeoutTime,
                    "thread" to Thread.currentThread().name
                ))
                PingResult(
                    latency = 0,
                    isSuccess = false,
                    timestamp = System.currentTimeMillis(),
                    error = "Timeout"
                )
            } catch (e: Exception) {
                val errorTime = System.currentTimeMillis()
                val totalErrorTime = errorTime - pingStartTime
                println("🔍 [PING_EXCEPTION] Ping test EXCEPTION for server ${server.name} - error: ${e.message}, total_time: ${totalErrorTime}ms, thread: ${Thread.currentThread().name}")

                logError("3-packet ping test failed for server ${server.name}: ${e.message}", mapOf(
                    "server" to server.name,
                    "total_time_ms" to totalErrorTime,
                    "thread" to Thread.currentThread().name,
                    "exception_type" to e.javaClass.simpleName
                ), e)
                PingResult(
                    latency = 0,
                    isSuccess = false,
                    timestamp = System.currentTimeMillis(),
                    error = e.message
                )
            }
        }
    }

    /**
     * NAME: performSinglePingAttempt
     *
     * DESCRIPTION:
     *     Performs a single ping attempt as part of the 3-packet concurrent test.
     *     Creates an independent socket and measures round-trip time.
     *     Enhanced with detailed debug logging for latency analysis.
     *
     * PARAMETERS:
     *     server - Server to ping
     *     addressToUse - Target server address (IP or hostname)
     *     protocolAddress - Address for protocol creation
     *     useResolvedIP - Whether using resolved IP
     *     packetIndex - Index of this packet (1-3) for logging
     *
     * RETURNS:
     *     Pair<Int, Boolean> - Pair of (latency in ms, success flag)
     */
    private suspend fun performSinglePingAttempt(
        server: ServerInfo,
        addressToUse: String,
        protocolAddress: String,
        useResolvedIP: Boolean,
        packetIndex: Int
    ): Pair<Int, Boolean> = withContext(Dispatchers.IO) {
        var socket: DatagramSocket? = null
        val attemptStartTime = System.currentTimeMillis()

        // 🔧 DEBUG: Log attempt start with thread info
        println("🔍 [DEBUG] Packet $packetIndex attempt START - server: ${server.name}, thread: ${Thread.currentThread().name}, time: $attemptStartTime")

        try {
            // 🔧 DEBUG: Log socket creation timing
            val socketCreateStart = System.currentTimeMillis()
            socket = DatagramSocket()
            val socketCreateTime = System.currentTimeMillis() - socketCreateStart
            println("🔍 [DEBUG] Packet $packetIndex socket created - time: ${socketCreateTime}ms, server: ${server.name}")

            // Socket protection with app disallowed list optimization
            val vpnService = ITforceVPNService.getInstance()
            if (vpnService != null) {
                val protectStart = System.currentTimeMillis()
                val protected = vpnService.protectUDPSocket(socket)
                val protectTime = System.currentTimeMillis() - protectStart
                println("🔍 [DEBUG] Packet $packetIndex socket protection - protected: $protected, time: ${protectTime}ms, server: ${server.name}")

                logDebug("Packet $packetIndex socket protection attempt", mapOf(
                    "protected" to protected,
                    "server" to server.serverName,
                    "packet" to packetIndex,
                    "protect_time_ms" to protectTime,
                    "optimization_note" to "app_disallowed_list_optimizes_protection"
                ))
            }

            logDebug("Packet $packetIndex socket created for ping", mapOf(
                "server" to server.serverName,
                "packet" to packetIndex,
                "socket_create_time_ms" to socketCreateTime,
                "protection_method" to "app_disallowed_list_optimization"
            ))

            // 🔧 DEBUG: Log connection setup timing
            val connectStart = System.currentTimeMillis()
            socket.connect(InetAddress.getByName(addressToUse), server.serverPort)
            socket.soTimeout = configuration.pingTimeout.toInt()
            val connectTime = System.currentTimeMillis() - connectStart
            println("🔍 [DEBUG] Packet $packetIndex socket connected - time: ${connectTime}ms, server: ${server.name}, timeout: ${configuration.pingTimeout}ms")

            // Create SDWAN protocol instance
            val protocolStart = System.currentTimeMillis()
            val protocol = sdwanProtocolFactory(protocolAddress, server.serverPort)
            val protocolTime = System.currentTimeMillis() - protocolStart
            println("🔍 [DEBUG] Packet $packetIndex protocol created - time: ${protocolTime}ms, server: ${server.name}")

            try {
                // Create and send OPEN packet for ping
                val packetCreateStart = System.currentTimeMillis()
                val openPacket = protocol.buildOpenPacketForPing(
                    username = "notExist",      // Same as iOS and Go backend
                    password = "mPassword",     // Same as iOS and Go backend
                    mtu = 1420u,                // Same as iOS and Go backend
                    encryptionMethod = com.panabit.client.protocol.models.EncryptionMethod.NONE
                )
                val packetCreateTime = System.currentTimeMillis() - packetCreateStart
                println("🔍 [DEBUG] Packet $packetIndex OPEN packet created - time: ${packetCreateTime}ms, server: ${server.name}")

                // Send ping packet first
                val sendStart = System.currentTimeMillis()
                protocol.sendPacketWithSocket(socket, openPacket, if (useResolvedIP) addressToUse else null)
                val sendTime = System.currentTimeMillis() - sendStart

                // 🔧 CRITICAL: Record start time AFTER sending packet (this is the actual latency measurement start)
                val startTime = System.currentTimeMillis()
                println("🔍 [DEBUG] Packet $packetIndex LATENCY MEASUREMENT START - start_time: $startTime, send_time: ${sendTime}ms, server: ${server.name}")

                // Wait for any response
                val responseReceived: Boolean
                val endTime: Long

                val responseReceiveResult = try {
                    val buffer = ByteArray(1024)
                    val packet = DatagramPacket(buffer, buffer.size)
                    println("🔍 [DEBUG] Packet $packetIndex waiting for response - server: ${server.name}, timeout: ${socket.soTimeout}ms")
                    socket.receive(packet)

                    // 🔧 CRITICAL: Record end time IMMEDIATELY after receiving response
                    val responseEndTime = System.currentTimeMillis()
                    println("🔍 [DEBUG] Packet $packetIndex RESPONSE RECEIVED - end_time: $responseEndTime, server: ${server.name}")
                    Pair(true, responseEndTime)
                } catch (e: java.net.SocketTimeoutException) {
                    // 🔧 CRITICAL: Record end time when timeout occurs
                    val timeoutEndTime = System.currentTimeMillis()
                    println("🔍 [DEBUG] Packet $packetIndex TIMEOUT - end_time: $timeoutEndTime, elapsed: ${timeoutEndTime - startTime}ms, server: ${server.name}")
                    Pair(false, timeoutEndTime)
                } catch (e: Exception) {
                    // 🔧 CRITICAL: Record end time when error occurs
                    val errorEndTime = System.currentTimeMillis()
                    println("🔍 [DEBUG] Packet $packetIndex RECEIVE ERROR - end_time: $errorEndTime, error: ${e.message}, server: ${server.name}")
                    Pair(false, errorEndTime)
                }

                responseReceived = responseReceiveResult.first
                endTime = responseReceiveResult.second

                // 🔧 CRITICAL: Calculate latency (this is the core measurement)
                val latency = (endTime - startTime).toInt()
                val totalAttemptTime = endTime - attemptStartTime

                println("🔍 [DEBUG] Packet $packetIndex LATENCY MEASUREMENT END - start: $startTime, end: $endTime, latency: ${latency}ms (CORRECTED), total_attempt: ${totalAttemptTime}ms, server: ${server.name}")

                if (responseReceived) {
                    println("🔍 [SUCCESS] Packet $packetIndex SUCCESSFUL - latency: ${latency}ms (CORRECTED: send→receive), server: ${server.name}")
                    logDebug("Packet $packetIndex successful", mapOf(
                        "server" to server.name,
                        "packet" to packetIndex,
                        "latency_corrected" to "${latency}ms",
                        "latency_calculation" to "send_to_receive",
                        "start_time" to startTime,
                        "end_time" to endTime,
                        "total_attempt_time" to "${totalAttemptTime}ms",
                        "socket_create_time" to "${socketCreateTime}ms",
                        "connect_time" to "${connectTime}ms",
                        "protocol_time" to "${protocolTime}ms",
                        "packet_create_time" to "${packetCreateTime}ms",
                        "send_time" to "${sendTime}ms"
                    ))
                } else {
                    println("🔍 [FAILED] Packet $packetIndex FAILED - no response, elapsed: ${latency}ms (CORRECTED: send→timeout), server: ${server.name}")
                    logDebug("Packet $packetIndex failed - no response", mapOf(
                        "server" to server.name,
                        "packet" to packetIndex,
                        "elapsed_time_corrected" to "${latency}ms",
                        "latency_calculation" to "send_to_timeout",
                        "start_time" to startTime,
                        "end_time" to endTime,
                        "total_attempt_time" to "${totalAttemptTime}ms"
                    ))
                }

                return@withContext Pair(if (responseReceived) latency else 0, responseReceived)

            } finally {
                val cleanupStart = System.currentTimeMillis()
                protocol.cleanup()
                val cleanupTime = System.currentTimeMillis() - cleanupStart
                println("🔍 [DEBUG] Packet $packetIndex protocol cleanup - time: ${cleanupTime}ms, server: ${server.name}")
            }

        } catch (e: Exception) {
            val errorTime = System.currentTimeMillis()
            val totalErrorTime = errorTime - attemptStartTime
            println("🔍 [ERROR] Packet $packetIndex EXCEPTION - error: ${e.message}, total_time: ${totalErrorTime}ms, server: ${server.name}, thread: ${Thread.currentThread().name}")

            logError("Ping attempt failed for packet $packetIndex", mapOf(
                "server" to server.name,
                "packet" to packetIndex,
                "error" to (e.message ?: "unknown"),
                "total_attempt_time" to "${totalErrorTime}ms",
                "thread" to Thread.currentThread().name
            ), e)

            return@withContext Pair(0, false)

        } finally {
            val finallyStart = System.currentTimeMillis()
            try {
                socket?.close()
                val closeTime = System.currentTimeMillis() - finallyStart
                println("🔍 [DEBUG] Packet $packetIndex socket closed - time: ${closeTime}ms, server: ${server.name}")
            } catch (e: Exception) {
                val closeErrorTime = System.currentTimeMillis() - finallyStart
                println("🔍 [ERROR] Packet $packetIndex socket close failed - time: ${closeErrorTime}ms, error: ${e.message}, server: ${server.name}")
                logWarn("Failed to close socket for packet $packetIndex", mapOf(
                    "server" to server.name,
                    "packet" to packetIndex,
                    "close_time" to "${closeErrorTime}ms"
                ), e)
            }

            val totalFinalTime = System.currentTimeMillis() - attemptStartTime
            println("🔍 [DEBUG] Packet $packetIndex attempt COMPLETE - total_time: ${totalFinalTime}ms, server: ${server.name}")
        }
    }

    /**
     * NAME: startUpdateTask
     *
     * DESCRIPTION:
     *     Starts background server list update task.
     *     Runs periodically based on configuration.updateInterval.
     */
    private fun startUpdateTask() {
        updateTask = serviceScope.launch {
            while (isActive && isRunning.get()) {
                try {
                    // Perform periodic server list updates from remote provider
                    if (serverListProvider != null) {
                        logDebug("Server list update task running - fetching from remote provider")
                        try {
                            fetchAndUpdateServerList()
                            logDebug("Server list update completed successfully")
                        } catch (e: Exception) {
                            logWarn("Server list update failed, keeping existing list: ${e.message}")
                            // Continue with existing server list on failure
                        }
                    } else {
                        logDebug("Server list update task running - no provider configured, skipping")
                    }

                    delay(configuration.updateInterval)
                } catch (e: Exception) {
                    logError("Server update task error: ${e.message}", e)
                    delay(configuration.updateInterval) // Continue despite errors
                }
            }
        }
    }

    /**
     * NAME: startPingTask
     *
     * DESCRIPTION:
     *     Starts background ping monitoring task.
     *     Runs periodically based on configuration.pingInterval.
     */
    private fun startPingTask() {
        logInfo("Starting background ping task", mapOf(
            "ping_interval_ms" to configuration.pingInterval,
            "ping_timeout_ms" to configuration.pingTimeout
        ))

        pingTask = serviceScope.launch {
            while (isActive && isRunning.get()) {
                try {
                    if (_servers.value.isNotEmpty()) {
                        logInfo("Starting periodic ping test", mapOf("server_count" to _servers.value.size))

                        // Execute ping and notify Flutter UI
                        pingAllServers()

                        // Send ping results to Flutter UI after periodic ping
                        try {
                            val platformHandler = com.panabit.client.MainActivity.getPlatformChannelHandler()
                            if (platformHandler != null) {
                                val updatedServers = getServers()
                                platformHandler.sendPingResultsEvent(updatedServers)
                                logInfo("Periodic ping results sent to Flutter UI", mapOf("server_count" to updatedServers.size))
                            } else {
                                logWarn("Platform handler not available for periodic ping results")
                            }
                        } catch (e: Exception) {
                            logWarn("Failed to send periodic ping results to Flutter", emptyMap(), e)
                        }
                    } else {
                        logDebug("Skipping periodic ping - no servers available")
                    }

                    delay(configuration.pingInterval)
                } catch (e: Exception) {
                    logError("Ping task error: ${e.message}", e)
                    delay(configuration.pingInterval) // Continue despite errors
                }
            }
        }

        logInfo("Background ping task started successfully")
    }

    /**
     * NAME: notifyUpdateCallbacks
     *
     * DESCRIPTION:
     *     Notifies all registered update callbacks about server list changes.
     *
     * PARAMETERS:
     *     servers - Updated server list
     */
    private suspend fun notifyUpdateCallbacks(servers: List<ServerInfo>) {
        callbackMutex.withLock {
            val callbacks = updateCallbacks.toList() // Create copy to avoid concurrent modification
            serviceScope.launch {
                callbacks.forEach { callback ->
                    try {
                        callback(servers)
                    } catch (e: Exception) {
                        logError("Update callback error: ${e.message}", e)
                    }
                }
            }
        }
    }

    /**
     * NAME: notifyStatusCallbacks
     *
     * DESCRIPTION:
     *     Notifies all registered status callbacks about server status changes.
     *
     * PARAMETERS:
     *     serverId - ID of server with status change
     *     status - New server status
     */
    private suspend fun notifyStatusCallbacks(serverId: String, status: ServerStatus) {
        callbackMutex.withLock {
            val callbacks = statusCallbacks.toList() // Create copy to avoid concurrent modification
            serviceScope.launch {
                callbacks.forEach { callback ->
                    try {
                        callback(serverId, status)
                    } catch (e: Exception) {
                        logError("Status callback error: ${e.message}", e)
                    }
                }
            }
        }
    }

    // MARK: - IP Resolution Management Implementation

    /**
     * NAME: getCachedServerIP
     *
     * DESCRIPTION:
     *     Gets cached IP address for a server hostname.
     *     Returns cached IP if available, null otherwise.
     *
     * PARAMETERS:
     *     hostname - Server hostname to lookup
     *
     * RETURNS:
     *     String? - Cached IP address or null if not available
     */
    override fun getCachedServerIP(hostname: String): String? {
        return serverIPCache[hostname]
    }

    /**
     * NAME: resolveServerIP
     *
     * DESCRIPTION:
     *     Resolves server hostname to IP address with caching.
     *     Uses cache first, performs DNS resolution if needed.
     *
     * PARAMETERS:
     *     hostname - Server hostname to resolve
     *
     * RETURNS:
     *     String? - Resolved IP address or null if failed
     */
    override suspend fun resolveServerIP(hostname: String): String? {
        // Check cache first
        serverIPCache[hostname]?.let { return it }

        // Resolve and cache
        val resolvedIP = resolveHostnameToIP(hostname)
        if (resolvedIP != null) {
            serverIPCache[hostname] = resolvedIP
        }
        return resolvedIP
    }

    /**
     * NAME: getAllCachedServerIPs
     *
     * DESCRIPTION:
     *     Gets all cached server IP addresses for route exclusion.
     *     Used by ConnectionManager to exclude server IPs from VPN routing.
     *
     * RETURNS:
     *     List<String> - List of all cached server IP addresses
     */
    override fun getAllCachedServerIPs(): List<String> {
        val allIPs = mutableListOf<String>()

        // Get IPs from current server list
        _servers.value.forEach { server ->
            serverIPCache[server.serverName]?.let { ip ->
                if (!allIPs.contains(ip)) {
                    allIPs.add(ip)
                }
            }
        }

        logDebug("Retrieved cached server IPs", mapOf(
            "server_count" to _servers.value.size,
            "cached_ip_count" to allIPs.size
        ))

        return allIPs
    }

    /**
     * NAME: resolveHostnameToIP
     *
     * DESCRIPTION:
     *     Resolves hostname to IP address using system DNS.
     *     Simple DNS resolution without complex caching logic.
     *
     * PARAMETERS:
     *     hostname - Hostname to resolve
     *
     * RETURNS:
     *     String? - Resolved IP address or null if failed
     */
    private suspend fun resolveHostnameToIP(hostname: String): String? = withContext(Dispatchers.IO) {
        try {
            // Check if hostname is already an IP address
            if (isValidIPAddress(hostname)) {
                return@withContext hostname
            }

            // Resolve hostname using system DNS
            val address = InetAddress.getByName(hostname)
            address.hostAddress
        } catch (e: Exception) {
            logWarn("Failed to resolve hostname", mapOf(
                "hostname" to hostname,
                "error" to (e.message ?: "unknown")
            ))
            null
        }
    }

    /**
     * NAME: isValidIPAddress
     *
     * DESCRIPTION:
     *     Checks if string is a valid IP address.
     *
     * PARAMETERS:
     *     address - Address string to check
     *
     * RETURNS:
     *     Boolean - true if valid IP address, false otherwise
     */
    private fun isValidIPAddress(address: String): Boolean {
        return try {
            InetAddress.getByName(address)
            // Additional check to ensure it's actually an IP address, not a hostname
            address.matches(Regex("^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$"))
        } catch (e: Exception) {
            false
        }
    }
}
