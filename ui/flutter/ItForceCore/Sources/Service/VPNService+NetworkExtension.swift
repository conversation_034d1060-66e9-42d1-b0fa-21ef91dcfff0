/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      VPNService+NetworkExtension.swift
 *
 * DESCRIPTION :    NetworkExtension integration extension for VPNService.
 *                  Contains iOS/macOS platform-specific VPN functionality including
 *                  permission management, tunnel configuration, and system integration.
 *                  Migrated from VPNService.swift to improve code organization.
 *
 * AUTHOR :         wei
 *
 * HISTORY :        03/07/2025 create - Migrated from VPNService.swift
 ******************************************************************************/

import Foundation
import NetworkExtension
import OSLog

// MARK: - VPNService NetworkExtension Management Extension

extension VPNService {
    
    // MARK: - NetworkExtension Properties
    // Note: These properties are defined in the main VPNService.swift file:
    // - tunnelManager: NETunnelProviderManager?
    // - isNetworkExtensionConfigured: Bool
    // - currentVPNConfiguration: VPNConfiguration?
    // - bundleIdentifier: String
    // - localizedDescription: String
    
    // MARK: - Permission Management
    
    /**
     * NAME: requestNetworkExtensionPermission
     *
     * DESCRIPTION:
     *     Requests VPN permission from user by creating and saving VPN configuration.
     *     Integrated from VPNManager to simplify architecture.
     *
     * PARAMETERS:
     *     configuration - VPN configuration for permission request
     *
     * RETURNS:
     *     Bool - Whether permission was granted
     *
     * THROWS:
     *     VPNServiceError - If permission request fails
     */
    public func requestNetworkExtensionPermission(with configuration: VPNConfiguration) async throws -> Bool {
        logger.info("Requesting VPN permission using Apple's best practices to avoid duplicate configurations")

        do {
            // First, load existing configurations following Apple's recommended approach
            // This prevents creating duplicate VPN profiles in system settings
            try await loadManagerFromPreferences()

            let manager: NETunnelProviderManager
            let isNewConfiguration: Bool

            if let existingManager = tunnelManager {
                // Reuse existing manager to prevent duplicate "ItForce VPN" entries
                logger.info("Found existing VPN configuration - reusing to prevent duplicates", metadata: [
                    "existing_enabled": "\(existingManager.isEnabled)",
                    "localized_description": existingManager.localizedDescription ?? "Unknown"
                ])
                manager = existingManager
                isNewConfiguration = false

                // Update the existing configuration with new connection parameters
                if let tunnelProtocol = manager.protocolConfiguration as? NETunnelProviderProtocol {
                    tunnelProtocol.serverAddress = configuration.serverAddress

                    // Ensure disconnectOnSleep is always set to false for existing configurations
                    tunnelProtocol.disconnectOnSleep = false

                    let providerConfiguration: [String: Any] = [
                        "serverAddress": configuration.serverAddress,
                        "serverPort": configuration.serverPort,
                        "username": configuration.username,
                        "password": configuration.password,
                        "mtu": configuration.mtu,
                        "encryptionMethod": configuration.encryptionMethod,
                        "dnsServers": configuration.dnsServers,
                        "excludedIPs": configuration.excludedIPs,
                        "tunnelIP": configuration.tunnelIP,
                        "routingMode": configuration.routingMode.rawValue,
                        "customRoutes": configuration.customRoutes
                    ]
                    tunnelProtocol.providerConfiguration = providerConfiguration

                    // logger.debug("Updated existing VPN configuration parameters") // Debug log commented for production
                }

                // Ensure manager is enabled (this is the permission indicator per Apple docs)
                manager.isEnabled = true
            } else {
                // Create new tunnel manager only if none exists
                logger.info("No existing VPN configuration found - creating new one")
                manager = try createTunnelManager(with: configuration)
                isNewConfiguration = true
            }

            // Save to preferences
            // For new configurations, this triggers the system permission dialog
            // For existing configurations, this updates the settings
            try await saveManagerToPreferences(manager)
            // logger.debug("VPN configuration saved to preferences", metadata: [
            //     "is_new_configuration": "\(isNewConfiguration)"
            // ]) // Debug log commented for production

            // Reload to verify save was successful and get updated state
            try await loadManagerFromPreferences()

            // Check if permission was granted using Apple's recommended approach
            // isEnabled is the authoritative indicator of VPN permission status
            let permissionGranted = tunnelManager?.isEnabled ?? false

            if permissionGranted {
                self.currentVPNConfiguration = configuration
                self.isNetworkExtensionConfigured = true
                logger.info("VPN permission granted by user", metadata: [
                    "configuration_type": isNewConfiguration ? "new" : "existing",
                    "is_enabled": "\(permissionGranted)"
                ])
            } else {
                logger.warning("VPN permission denied by user", metadata: [
                    "configuration_type": isNewConfiguration ? "new" : "existing",
                    "is_enabled": "\(permissionGranted)"
                ])
            }

            return permissionGranted

        } catch {
            logger.error("Failed to request VPN permission", metadata: [
                "error": error.localizedDescription
            ])
            throw VPNServiceError.configurationInvalid("Failed to request VPN permission: \(error.localizedDescription)")
        }
    }
    
    /**
     * NAME: checkVPNPermissionStatus
     *
     * DESCRIPTION:
     *     Checks VPN permission status with user-friendly messages.
     *     Integrated NetworkExtension management to eliminate VPNPermissionHandler dependency.
     *
     * RETURNS:
     *     VPNPermissionResult - Current permission status with user message
     */
    public func checkVPNPermissionStatus() async -> VPNPermissionResult {
        let status = await checkNetworkExtensionPermissionStatus()

        switch status {
        case .notDetermined:
            return VPNPermissionResult(
                granted: false,
                status: .notDetermined,
                userMessage: "需要请求VPN权限才能建立连接"
            )

        case .denied:
            return VPNPermissionResult(
                granted: false,
                status: .denied,
                userMessage: "VPN权限被拒绝，请在设置中手动启用"
            )

        case .restricted:
            return VPNPermissionResult(
                granted: false,
                status: .restricted,
                userMessage: "VPN功能被系统限制，请检查家长控制或企业策略设置"
            )

        case .authorized:
            return VPNPermissionResult(
                granted: true,
                status: .authorized,
                userMessage: "VPN权限已授权"
            )
        }
    }
    
    /**
     * NAME: checkNetworkExtensionPermissionStatus
     *
     * DESCRIPTION:
     *     Checks current VPN permission status without requesting permission.
     *     Uses Apple's recommended approach: loadAllFromPreferences + isEnabled check.
     *
     *     According to Apple documentation:
     *     - isEnabled = true: VPN configuration is authorized and enabled
     *     - isEnabled = false: VPN configuration exists but is disabled (user denied or disabled)
     *     - No configuration found: Permission not yet requested (.notDetermined)
     *
     * RETURNS:
     *     VPNPermissionStatus - Current permission status based on Apple's best practices
     */
    internal func checkNetworkExtensionPermissionStatus() async -> VPNPermissionStatus {
        do {
            let managers = try await NETunnelProviderManager.loadAllFromPreferences()

            logger.info("Loaded VPN configurations from preferences", metadata: [
                "total_managers": "\(managers.count)",
                "bundle_identifier": bundleIdentifier
            ])

            // Log all existing managers for debugging
            for (index, manager) in managers.enumerated() {
                if let tunnelProtocol = manager.protocolConfiguration as? NETunnelProviderProtocol {
                    logger.info("Found VPN configuration #\(index)", metadata: [
                        "bundle_id": tunnelProtocol.providerBundleIdentifier ?? "Unknown",
                        "server_address": tunnelProtocol.serverAddress ?? "Unknown",
                        "is_enabled": "\(manager.isEnabled)",
                        "localized_description": manager.localizedDescription ?? "Unknown"
                    ])
                }
            }

            if managers.isEmpty {
                logger.warning("No VPN configurations found - this might indicate NetworkExtension is not properly embedded in app bundle")
                return .notDetermined
            }

            // Find our tunnel manager by bundle identifier (Apple's recommended approach)
            let ourManager = managers.first { manager in
                guard let tunnelProtocol = manager.protocolConfiguration as? NETunnelProviderProtocol else {
                    return false
                }
                let matches = tunnelProtocol.providerBundleIdentifier == bundleIdentifier
                // logger.debug("Checking manager bundle ID", metadata: [
                //     "expected": bundleIdentifier,
                //     "actual": tunnelProtocol.providerBundleIdentifier ?? "nil",
                //     "matches": "\(matches)"
                // ]) // Debug log commented for production
                return matches
            }

            guard let manager = ourManager else {
                logger.warning("No matching VPN configuration found for our bundle identifier", metadata: [
                    "expected_bundle_id": bundleIdentifier,
                    "available_managers": "\(managers.count)"
                ])
                return .notDetermined
            }

            // Use isEnabled as the authoritative permission status indicator
            // This is the correct way according to Apple's documentation
            let permissionStatus: VPNPermissionStatus = manager.isEnabled ? .authorized : .denied

            logger.info("VPN permission status determined", metadata: [
                "is_enabled": "\(manager.isEnabled)",
                "permission_status": "\(permissionStatus)",
                "localized_description": manager.localizedDescription ?? "Unknown"
            ])

            return permissionStatus

        } catch {
            logger.error("Failed to check VPN permission status", metadata: [
                "error": error.localizedDescription,
                "error_type": "\(type(of: error))"
            ])
            return .notDetermined
        }
    }
    
    // MARK: - Configuration Management
    
    /**
     * NAME: loadExistingVPNConfiguration
     *
     * DESCRIPTION:
     *     Loads existing VPN configuration from system preferences.
     *     Integrated NetworkExtension management to eliminate VPNManager dependency.
     *
     * RETURNS:
     *     Bool - Whether existing configuration was found and loaded
     */
    public func loadExistingVPNConfiguration() async -> Bool {
        do {
            try await loadManagerFromPreferences()

            if tunnelManager != nil {
                isNetworkExtensionConfigured = true
                logger.info("Existing VPN configuration loaded successfully")
                return true
            } else {
                logger.info("No existing VPN configuration found")
                return false
            }
        } catch {
            logger.error("Failed to load existing VPN configuration", metadata: [
                "error": error.localizedDescription
            ])
            return false
        }
    }
    
    // MARK: - Status Management
    
    /**
     * NAME: getVPNConnectionStatus
     *
     * DESCRIPTION:
     *     Gets current VPN connection status from system.
     *     Integrated NetworkExtension management to eliminate VPNManager dependency.
     *
     * RETURNS:
     *     NEVPNStatus - Current VPN connection status
     */
    public func getVPNConnectionStatus() async -> NEVPNStatus {
        guard let manager = tunnelManager else {
            return .invalid
        }

        return manager.connection.status
    }
    
    /**
     * NAME: getVPNStatus
     *
     * DESCRIPTION:
     *     Gets current VPN connection status from NetworkExtension.
     *
     * RETURNS:
     *     NEVPNStatus - Current VPN status
     */
    internal func getVPNStatus() async -> NEVPNStatus {
        guard let manager = tunnelManager else {
            return .invalid
        }

        // Reload manager to get latest status
        do {
            try await manager.loadFromPreferences()
            return manager.connection.status
        } catch {
            logger.warning("Failed to load VPN manager preferences", metadata: [
                "error": error.localizedDescription
            ])
            return .invalid
        }
    }

    // MARK: - Tunnel Management

    /**
     * NAME: startNetworkExtensionTunnel
     *
     * DESCRIPTION:
     *     Starts VPN tunnel with current configuration.
     *     Integrated from VPNManager to simplify architecture.
     *
     * THROWS:
     *     VPNServiceError - If tunnel start fails
     */
    internal func startNetworkExtensionTunnel() async throws {
        guard isNetworkExtensionConfigured, let manager = tunnelManager else {
            throw VPNServiceError.configurationInvalid("NetworkExtension not configured")
        }

        guard let configuration = currentVPNConfiguration else {
            throw VPNServiceError.configurationInvalid("No VPN configuration available")
        }

        logger.info("Starting VPN tunnel", metadata: [
            "server": configuration.serverAddress,
            "port": "\(configuration.serverPort)",
            "routing_mode": configuration.routingMode.rawValue,
            "custom_routes_count": "\(configuration.customRoutes.count)"
        ])

        do {
            // Prepare tunnel options
            let options: [String: NSObject] = [
                "serverAddress": configuration.serverAddress as NSString,
                "serverPort": NSNumber(value: configuration.serverPort),
                "username": configuration.username as NSString,
                "password": configuration.password as NSString,
                "mtu": NSNumber(value: configuration.mtu),
                "encryptionMethod": NSNumber(value: configuration.encryptionMethod),
                "dnsServers": configuration.dnsServers as NSArray,
                "excludedIPs": configuration.excludedIPs as NSArray,
                "tunnelIP": configuration.tunnelIP as NSString,
                "routingMode": configuration.routingMode.rawValue as NSString,
                "customRoutes": configuration.customRoutes as NSArray
            ]

            // Start VPN tunnel
            try manager.connection.startVPNTunnel(options: options)

            logger.info("VPN tunnel start request sent successfully")

        } catch {
            logger.error("Failed to start VPN tunnel", metadata: [
                "error": error.localizedDescription
            ])
            throw VPNServiceError.configurationInvalid("Failed to start VPN tunnel: \(error.localizedDescription)")
        }
    }

    /**
     * NAME: stopNetworkExtensionTunnel
     *
     * DESCRIPTION:
     *     Stops VPN tunnel.
     *     Integrated from VPNManager to simplify architecture.
     */
    internal func stopNetworkExtensionTunnel() {
        guard let manager = tunnelManager else {
            logger.warning("No tunnel manager available to stop")
            return
        }

        logger.info("Stopping VPN tunnel")
        manager.connection.stopVPNTunnel()
    }

    // MARK: - Private Helper Methods

    /**
     * NAME: createTunnelManager
     *
     * DESCRIPTION:
     *     Creates NETunnelProviderManager with specified configuration.
     *     Integrated from VPNManager to simplify architecture.
     *
     * PARAMETERS:
     *     configuration - VPN configuration for tunnel manager
     *
     * RETURNS:
     *     NETunnelProviderManager - Configured tunnel manager
     *
     * THROWS:
     *     VPNServiceError - If manager creation fails
     */
    private func createTunnelManager(with configuration: VPNConfiguration) throws -> NETunnelProviderManager {
        let manager = NETunnelProviderManager()
        manager.localizedDescription = localizedDescription

        // Configure tunnel provider protocol
        let tunnelProtocol = NETunnelProviderProtocol()
        tunnelProtocol.providerBundleIdentifier = bundleIdentifier
        tunnelProtocol.serverAddress = configuration.serverAddress

        // Critical fix: Prevent VPN disconnection during device sleep
        // This ensures VPN connection remains active when device is locked/sleeping
        tunnelProtocol.disconnectOnSleep = false

        // Set provider configuration (this will be passed to NetworkExtension)
        let providerConfiguration: [String: Any] = [
            "serverAddress": configuration.serverAddress,
            "serverPort": configuration.serverPort,
            "username": configuration.username,
            "password": configuration.password,
            "mtu": configuration.mtu,
            "encryptionMethod": configuration.encryptionMethod,
            "dnsServers": configuration.dnsServers,
            "excludedIPs": configuration.excludedIPs,
            "tunnelIP": configuration.tunnelIP,
            "routingMode": configuration.routingMode.rawValue,
            "customRoutes": configuration.customRoutes
        ]

        tunnelProtocol.providerConfiguration = providerConfiguration

        manager.protocolConfiguration = tunnelProtocol
        manager.isEnabled = true

        logger.info("Created tunnel manager", metadata: [
            "bundle_id": bundleIdentifier,
            "server": configuration.serverAddress,
            "port": "\(configuration.serverPort)"
        ])

        return manager
    }

    /**
     * NAME: saveManagerToPreferences
     *
     * DESCRIPTION:
     *     Saves tunnel manager to system preferences.
     *     Integrated from VPNManager to simplify architecture.
     *
     * PARAMETERS:
     *     manager - Tunnel manager to save
     *
     * THROWS:
     *     VPNServiceError - If save operation fails
     */
    private func saveManagerToPreferences(_ manager: NETunnelProviderManager) async throws {
        try await manager.saveToPreferences()
        logger.info("Tunnel manager saved to preferences")
    }

    /**
     * NAME: loadManagerFromPreferences
     *
     * DESCRIPTION:
     *     Loads tunnel manager from system preferences.
     *     Integrated from VPNManager to simplify architecture.
     *
     * THROWS:
     *     VPNServiceError - If load operation fails
     */
    internal func loadManagerFromPreferences() async throws {
        let managers = try await NETunnelProviderManager.loadAllFromPreferences()

        // Find our tunnel manager
        tunnelManager = managers.first { manager in
            guard let tunnelProtocol = manager.protocolConfiguration as? NETunnelProviderProtocol else {
                return false
            }
            return tunnelProtocol.providerBundleIdentifier == bundleIdentifier
        }

        if tunnelManager != nil {
            logger.info("Tunnel manager loaded from preferences")
        } else {
            logger.warning("No tunnel manager found in preferences")
        }
    }
}
