/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      manager.go
 *
 * DESCRIPTION :    Connection manager implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        10/06/2025 create
 ******************************************************************************/

package manager

import (
	"context"
	"fmt"
	"net"
	"sync"
	"time"

	"mobile/internal/common/errors"
	"mobile/internal/common/goroutine"
	"mobile/internal/common/logger"
	"mobile/internal/common/monitor"
	"mobile/internal/common/pool"
	"mobile/internal/connection/server"
	"mobile/internal/platform/network"
	"mobile/internal/platform/tun"
	"mobile/internal/protocol/encryption"
	"mobile/internal/protocol/tunnel"
)

/*****************************************************************************
 * NAME: ServerManager
 *
 * DESCRIPTION:
 *     Server manager interface for obtaining physical interface information
 *
 * METHODS:
 *     GetPhysicalInterfaceInfo() - Get physical interface information
 *     RegisterNetworkChangeCallback(callback func()) - Register network change callback
 *****************************************************************************/
type ServerManager interface {
	GetPhysicalInterfaceInfo() *network.PhysicalInterfaceInfo
	RegisterNetworkChangeCallback(callback func())
}

/*****************************************************************************
 * NAME: Config
 *
 * DESCRIPTION:
 *     Connection configuration structure
 *
 * FIELDS:
 *     ServerAddress     - Server address
 *     ServerPort        - Server port
 *     Username          - Username
 *     Password          - Password
 *     MTU               - Maximum transmission unit
 *     Encryption        - Encryption method
 *     Timeout           - Connection timeout
 *     RetryCount        - Retry count
 *     RetryInterval     - Retry interval
 *     HeartbeatInterval - Heartbeat interval
 *     ExtraConfig       - Extra configuration
 *****************************************************************************/
type Config struct {
	ServerAddress     string            // Server address
	ServerPort        int               // Server port
	Username          string            // Username
	Password          string            // Password
	MTU               int               // Maximum transmission unit
	Encryption        uint8             // Encryption method
	Timeout           time.Duration     // Connection timeout
	RetryCount        int               // Retry count
	RetryInterval     time.Duration     // Retry interval
	HeartbeatInterval time.Duration     // Heartbeat interval
	ExtraConfig       map[string]string // Extra configuration
}

/*****************************************************************************
 * NAME: Status
 *
 * DESCRIPTION:
 *     Connection status structure
 *
 * FIELDS:
 *     State         - Current state
 *     StateString   - State string representation
 *     SessionID     - Session ID
 *     Token         - Authentication token
 *     LastHeartbeat - Last heartbeat time
 *     ConnectedTime - Connection time
 *     Config        - Configuration information
 *     ErrorCode     - Error code
 *     ErrorMessage  - Error message
 *     Status        - API status (disconnected, connecting, connected, error)
 *     Message       - Status message
 *     Server        - Current connected server
 *****************************************************************************/
type Status struct {
	State         uint8             // Current state
	StateString   string            // State string representation
	SessionID     uint16            // Session ID
	Token         uint32            // Authentication token
	LastHeartbeat time.Time         // Last heartbeat time
	ConnectedTime time.Time         // Connection time
	Config        map[string]string // Configuration information
	ErrorCode     int               // Error code
	ErrorMessage  string            // Error message
	Status        string            // API status (disconnected, connecting, connected, error)
	Message       string            // Status message
	Server        *server.Server    // Current connected server
}

/*****************************************************************************
 * NAME: StatusNotifier
 *
 * DESCRIPTION:
 *     Status notification interface
 *
 * METHODS:
 *     NotifyStatusChange(status *Status) - Notify status change
 *     NotifyReconnectRequired(reason, message string) - Notify reconnect required
 *****************************************************************************/
type StatusNotifier interface {
	NotifyStatusChange(status *Status)
	NotifyReconnectRequired(reason, message string)
}

/*****************************************************************************
 * NAME: InterfaceInfo
 *
 * DESCRIPTION:
 *     Interface information structure
 *
 * FIELDS:
 *     InterfaceName  - Physical interface name
 *     LocalIP        - Local IP address (physical interface IP)
 *     TunIP          - TUN device IP address
 *     InterfaceIndex - Physical interface index
 *     InterfaceLUID  - Physical interface LUID
 *****************************************************************************/
type InterfaceInfo struct {
	InterfaceName  string `json:"interface_name"`  // Physical interface name
	LocalIP        string `json:"local_ip"`        // Local IP address (physical interface IP)
	TunIP          string `json:"tun_ip"`          // TUN device IP address
	InterfaceIndex int    `json:"interface_index"` // Physical interface index
	InterfaceLUID  uint64 `json:"interface_luid"`  // Physical interface LUID
}

/*****************************************************************************
 * NAME: RoutingMode
 *
 * DESCRIPTION:
 *     Routing mode type definition
 *****************************************************************************/
type RoutingMode string

const (
	RoutingModeAll    RoutingMode = "all"    // Route all traffic
	RoutingModeCustom RoutingMode = "custom" // Route by network segments
)

/*****************************************************************************
 * NAME: RoutingSettings
 *
 * DESCRIPTION:
 *     Routing settings structure
 *
 * FIELDS:
 *     Mode         - Routing mode
 *     CustomRoutes - Custom routes, comma-separated CIDR list
 *****************************************************************************/
type RoutingSettings struct {
	Mode         RoutingMode `json:"mode"`                    // Routing mode
	CustomRoutes string      `json:"custom_routes,omitempty"` // Custom routes, comma-separated CIDR list
}

/*****************************************************************************
 * NAME: TrafficStats
 *
 * DESCRIPTION:
 *     Traffic statistics structure
 *
 * FIELDS:
 *     TotalUpload   - Total uploaded bytes
 *     TotalDownload - Total downloaded bytes
 *     UploadSpeed   - Upload speed (bytes/second)
 *     DownloadSpeed - Download speed (bytes/second)
 *     LastUpdate    - Last update time
 *****************************************************************************/
type TrafficStats struct {
	TotalUpload   int64     // Total uploaded bytes
	TotalDownload int64     // Total downloaded bytes
	UploadSpeed   int64     // Upload speed (bytes/second)
	DownloadSpeed int64     // Download speed (bytes/second)
	LastUpdate    time.Time // Last update time
}

/*****************************************************************************
 * NAME: Manager
 *
 * DESCRIPTION:
 *     Connection manager structure for handling VPN connections
 *
 * FIELDS:
 *     config                - Connection configuration
 *     stateMachine          - State machine for connection states
 *     conn                  - Network connection
 *     encryptor             - Encryption handler
 *     log                   - Logger instance
 *     mutex                 - Read-write mutex for thread safety
 *     ctx                   - Context for cancellation
 *     cancel                - Cancel function
 *     packetCh              - Packet channel
 *     dataCh                - Data channel
 *     fragmentQueue         - Fragment queue for packet reassembly
 *     heartbeatTicker       - Heartbeat timer
 *     tunDevice             - TUN device interface
 *     currentServer         - Currently connected server
 *     serverIPs             - Resolved server IP addresses
 *     physicalIface         - Physical interface information (cached)
 *     serverManager         - Server manager interface
 *     routingSettings       - Routing configuration
 *     autoReconnectEnabled  - Auto-reconnect enabled flag
 *     autoReconnectAttempts - Current reconnect attempt count
 *     statusNotifier        - Status notification interface
 *     trafficStats          - Traffic statistics
 *     lastUpload            - Last upload bytes (for speed calculation)
 *     lastDownload          - Last download bytes (for speed calculation)
 *     goroutineManager      - Goroutine manager for performance optimization
 *     perfMonitor           - Performance monitor
 *     bufferPool            - Buffer pool for memory optimization
 *     packetsReceived       - Atomic counter for received packets
 *     packetsSent           - Atomic counter for sent packets
 *     bytesReceived         - Atomic counter for received bytes
 *     bytesSent             - Atomic counter for sent bytes
 *     started               - Started flag
 *     connected             - Connected flag
 *****************************************************************************/
type Manager struct {
	// Basic configuration and state
	config       Config               // Connection configuration
	stateMachine *StateMachine        // State machine for connection states
	conn         net.Conn             // Network connection
	encryptor    encryption.Encryptor // Encryption handler
	log          logger.Logger        // Logger instance
	mutex        sync.RWMutex         // Read-write mutex for thread safety
	ctx          context.Context      // Context for cancellation
	cancel       context.CancelFunc   // Cancel function

	// Channels and queues
	packetCh      chan *tunnel.Packet   // Packet channel
	dataCh        chan []byte           // Data channel
	fragmentQueue *tunnel.FragmentQueue // Fragment queue for packet reassembly

	// Timers and devices
	heartbeatTicker *time.Ticker // Heartbeat timer
	tunDevice       tun.Device   // TUN device interface

	// Server and network information
	currentServer   *server.Server  // Currently connected server
	serverIPs       []net.IP        // Resolved server IP addresses
	physicalIface   *InterfaceInfo  // Physical interface information (cached)
	serverManager   *server.Manager // Server manager instance (concrete type for IP cache access)
	routingSettings RoutingSettings // Routing configuration

	// Auto-reconnect settings
	autoReconnectEnabled  bool // Auto-reconnect enabled flag
	autoReconnectAttempts int  // Current reconnect attempt count

	// Status notification and statistics
	statusNotifier StatusNotifier // Status notification interface
	trafficStats   TrafficStats   // Traffic statistics
	lastUpload     int64          // Last upload bytes (for speed calculation)
	lastDownload   int64          // Last download bytes (for speed calculation)

	// Performance optimization components
	goroutineManager *goroutine.Manager          // Goroutine manager for performance optimization
	perfMonitor      *monitor.PerformanceMonitor // Performance monitor
	bufferPool       pool.Pool                   // Buffer pool for memory optimization

	// Atomic counters for statistics
	packetsReceived uint64 // Atomic counter for received packets
	packetsSent     uint64 // Atomic counter for sent packets
	bytesReceived   uint64 // Atomic counter for received bytes
	bytesSent       uint64 // Atomic counter for sent bytes

	// State flags
	started   bool // Started flag
	connected bool // Connected flag
}

// Server management methods moved to connection_server.go

// Auto-reconnect methods moved to connection_reconnect.go

/*****************************************************************************
 * NAME: NewManager
 *
 * DESCRIPTION:
 *     Create a new connection manager instance
 *
 * PARAMETERS:
 *     config - Connection configuration
 *     log - Logger instance
 *     tunDevice - TUN device interface
 *     serverManager - Server manager interface (optional, for physical interface info)
 *
 * RETURNS:
 *     *Manager - Created connection manager instance
 *****************************************************************************/
func NewManager(config Config, log logger.Logger, tunDevice tun.Device, serverManager *server.Manager) *Manager {
	// Set default values
	if config.ServerPort == 0 {
		config.ServerPort = tunnel.DefaultPort
	}
	if config.MTU == 0 {
		config.MTU = tunnel.DefaultMTU
	}
	if config.Timeout == 0 {
		config.Timeout = time.Duration(tunnel.DefaultTimeout) * time.Millisecond
	}
	if config.RetryCount == 0 {
		config.RetryCount = tunnel.DefaultRetryCount
	}
	if config.RetryInterval == 0 {
		config.RetryInterval = time.Duration(tunnel.DefaultRetryInterval) * time.Millisecond
	}
	if config.HeartbeatInterval == 0 {
		config.HeartbeatInterval = time.Duration(tunnel.DefaultHeartbeat) * time.Millisecond
	}

	ctx, cancel := context.WithCancel(context.Background())

	// Create modular logger
	moduleLogger := log.WithModule("connection-manager")

	// Initialize performance optimization components
	goroutineManager := goroutine.NewManager(moduleLogger)
	perfMonitor := monitor.NewPerformanceMonitor(moduleLogger, 30*time.Second)

	// Initialize global object pool if not already initialized
	if pool.GetGlobalManager() == nil {
		pool.InitGlobalPools(moduleLogger)
	}

	// Get packet buffer pool
	bufferPool := pool.PacketBufferPool

	return &Manager{
		// Basic configuration
		config: config,
		log:    moduleLogger,
		ctx:    ctx,
		cancel: cancel,

		// Channels and queues - use larger buffers for better performance
		packetCh:      make(chan *tunnel.Packet, 10240),
		dataCh:        make(chan []byte, 10240),
		fragmentQueue: tunnel.NewFragmentQueue(log),

		// Devices and state
		tunDevice:      tunDevice,
		statusNotifier: nil,

		// Server manager
		serverManager: serverManager,

		// Auto-reconnect settings
		autoReconnectEnabled:  false, // Disable auto-reconnect by default - UI will handle reconnection
		autoReconnectAttempts: 0,     // Initial reconnect attempt count is 0

		// Routing settings
		routingSettings: RoutingSettings{
			Mode:         RoutingModeAll,
			CustomRoutes: "",
		},

		// Performance optimization components
		goroutineManager: goroutineManager,
		perfMonitor:      perfMonitor,
		bufferPool:       bufferPool,

		// Initialize state
		started:   false,
		connected: false,
	}
}

/*****************************************************************************
 * NAME: Start
 *
 * DESCRIPTION:
 *     Start the connection manager, initialize state machine and packet handlers
 *
 * RETURNS:
 *     error - Start error if any
 *****************************************************************************/
func (m *Manager) Start() error {
	// 	m.mutex.Lock()
	// 	defer m.mutex.Unlock()

	if m.started {
		return errors.NewWithCode(errors.CodeAlreadyExists,
			"connection manager already started").(*errors.DetailedError).
			WithComponent("connection").WithOperation("start")
	}

	m.log.Info("Starting connection manager")

	// Start performance monitor
	if err := m.perfMonitor.Start(); err != nil {
		return errors.WrapWithCode(errors.CodeInternal, err,
			"failed to start performance monitor").(*errors.DetailedError).
			WithComponent("connection").WithOperation("start")
	}

	// Initialize TUN subsystem
	err := tun.Initialize(m.log)
	if err != nil {
		return errors.WrapWithCode(errors.CodeTunnelError, err,
			"failed to initialize TUN subsystem").(*errors.DetailedError).
			WithComponent("connection").WithOperation("start")
	}

	// Create TUN device if not already created
	if m.tunDevice == nil {
		m.tunDevice, err = tun.CreateDevice()
		if err != nil {
			return errors.WrapWithCode(errors.CodeTunnelError, err,
				"failed to create TUN device").(*errors.DetailedError).
				WithComponent("connection").WithOperation("start")
		}
		m.log.Info("TUN device created successfully")
	}

	// Log TUN device information
	if m.tunDevice != nil {
		m.log.Debug("TUN device details",
			logger.String("device_type", fmt.Sprintf("%T", m.tunDevice)),
			logger.String("device_name", m.tunDevice.Name()),
			logger.Int("device_mtu", m.tunDevice.MTU()),
			logger.Bool("device_is_up", m.tunDevice.IsUp()))
	}

	// Start TUN device in background, don't block waiting
	m.log.Info("Starting TUN device in background")
	_, err = m.goroutineManager.Start("tun-device-starter", func(ctx context.Context) error {
		m.log.Debug("Starting TUN device in goroutine")
		err := tun.StartDevice()
		if err != nil {
			m.log.Error("Failed to start TUN device in background", logger.ErrorField(err))
			return fmt.Errorf("failed to start TUN device: %w", err)
		} else {
			m.log.Info("TUN device started successfully in background")
			return nil
		}
	})
	if err != nil {
		return errors.WrapWithCode(errors.CodeInternal, err,
			"failed to start TUN device starter goroutine").(*errors.DetailedError).
			WithComponent("connection").WithOperation("start")
	}

	// Create state machine
	m.stateMachine = NewStateMachine(m.log)

	// Register state change callback
	m.stateMachine.RegisterStateChangeCallback(func(oldState, newState uint8) {
		// Get current status and notify status notifier
		status := m.GetStatus()
		m.notifyStatusChange(&status)

		// Update performance monitoring metrics
		m.perfMonitor.SetGauge("connection_state", newState)
	})

	// Register packet handlers
	m.registerPacketHandlers()

	// Start traffic statistics updater goroutine
	_, err = m.goroutineManager.Start("traffic-stats-updater", m.runTrafficStatsUpdater)
	if err != nil {
		return errors.WrapWithCode(errors.CodeInternal, err,
			"failed to start traffic stats updater").(*errors.DetailedError).
			WithComponent("connection").WithOperation("start")
	}

	// Register network change callback to server manager
	if m.serverManager != nil {
		m.serverManager.RegisterNetworkChangeCallback(m.handleNetworkChange)
		m.log.Info("Registered network change callback for auto-reconnect")
	}

	m.started = true
	m.log.Info("Connection manager started successfully")
	return nil
}

/*****************************************************************************
 * NAME: cleanupWithError
 *
 * DESCRIPTION:
 *     Clean up resources and return error
 *
 * PARAMETERS:
 *     err - Original error
 *     errorCode - Error code
 *     errorMessage - Error message
 *
 * RETURNS:
 *     error - Original error passed through
 *****************************************************************************/
func (m *Manager) cleanupWithError(err error, errorCode int, errorMessage string) error {
	m.log.Info("Cleaning up connection due to error",
		logger.ErrorField(err),
		logger.Int("error_code", errorCode),
		logger.String("error_message", errorMessage))

	// Cancel context, this will stop all goroutines
	if m.cancel != nil {
		m.cancel()
		m.cancel = nil
	}

	// Stop heartbeat timer
	if m.heartbeatTicker != nil {
		m.heartbeatTicker.Stop()
		m.heartbeatTicker = nil
	}

	// Close connection
	if m.conn != nil {
		m.conn.Close()
		m.conn = nil
	}

	// Reset connection state
	m.connected = false

	// Set error state
	if errorCode != 0 {
		// Set different states based on error type
		if errorCode >= 200 && errorCode < 300 {
			// Authentication related errors, set to auth failure state
			m.stateMachine.SetState(StateAuthFail)
			m.stateMachine.SetError(errorCode, errorMessage)
		} else {
			// Other errors, set error info but maintain current state or set to closed state
			m.stateMachine.SetError(errorCode, errorMessage)
			m.stateMachine.SetState(StateClosed)
		}
	} else {
		// If no specific error code, set to closed state for reconnection
		m.stateMachine.SetState(StateClosed)
	}

	// Notify status change
	status := m.GetStatus()
	m.notifyStatusChange(&status)

	m.log.Info("Connection cleanup completed, ready for reconnection")
	return err
}

// TUN device management methods moved to connection_tun.go

/*****************************************************************************
 * NAME: SetCredentials
 *
 * DESCRIPTION:
 *     Sets user credentials for VPN authentication
 *
 * PARAMETERS:
 *     username - User login name
 *     password - User password
 *****************************************************************************/
func (m *Manager) SetCredentials(username, password string) {
	m.config.Username = username
	m.config.Password = password
	m.log.Info("Updated user credentials",
		logger.String("username", username),
		logger.Bool("has_password", len(password) > 0))
}

/*****************************************************************************
 * NAME: AuthAndConnect
 *
 * DESCRIPTION:
 *     Authenticate and connect to VPN server (one-step operation)
 *
 * PARAMETERS:
 *     serverAddress - Server address
 *     serverPort - Server port
 *
 * RETURNS:
 *     error - Connection error if any
 *****************************************************************************/
func (m *Manager) AuthAndConnect(serverAddress string, serverPort int) error {
	// Start performance tracking
	tracker := m.perfMonitor.TrackRequest("auth_and_connect")
	defer func() {
		if tracker != nil {
			tracker.Finish(m.connected)
		}
	}()

	// 	m.mutex.Lock()
	// 	defer m.mutex.Unlock()

	// Check if state machine is initialized
	if m.stateMachine == nil {
		return errors.NewWithCode(errors.CodeFailedPrecondition,
			"connection manager not started, call Start() first").(*errors.DetailedError).
			WithComponent("connection").WithOperation("auth_and_connect")
	}

	m.log.Info("Starting authentication and connection process",
		logger.String("server", serverAddress),
		logger.Int("port", serverPort))

	// Disconnect existing connection and reset state
	if m.connected || m.stateMachine.GetState() != StateClosed {
		m.log.Info("Disconnecting existing connection before new connection attempt")
		m.disconnectInternal()
		// Wait a short time to ensure cleanup is complete
		time.Sleep(100 * time.Millisecond)
	}

	// Ensure state machine is in connectable state
	if m.stateMachine.GetState() == StateAuthFail {
		m.log.Info("Resetting state machine from auth failure state")
		m.stateMachine.SetState(StateClosed)
	}

	// Ensure TUN device is ready
	err := m.ensureTunDeviceReady(15 * time.Second)
	if err != nil {
		m.log.Error("TUN device not ready", logger.ErrorField(err))
		return errors.WrapWithCode(errors.CodeTunnelError, err,
			"TUN device not ready").(*errors.DetailedError).
			WithComponent("connection").WithOperation("auth_and_connect")
	}

	// Create new context for unified resource management
	m.ctx, m.cancel = context.WithCancel(context.Background())

	// Update server address if provided
	if serverAddress != "" {
		m.config.ServerAddress = serverAddress
	}

	// Create or update encryptor - use username and password from config
	m.encryptor, err = encryption.NewEncryptor(m.config.Encryption, m.config.Username, m.config.Password, m.log)
	if err != nil {
		return fmt.Errorf("failed to create encryptor: %w", err)
	}

	// Reset traffic statistics
	m.log.Debug("Resetting traffic stats for new connection")
	m.resetTrafficStats()

	// Set state to initialization
	m.stateMachine.SetState(StateInit)

	// Use default port if port is 0
	port := serverPort
	if port == 0 {
		port = m.config.ServerPort
	}

	// Resolve server address
	serverAddr := net.JoinHostPort(m.config.ServerAddress, fmt.Sprintf("%d", port))
	m.log.Info("Resolving server address",
		logger.String("address", serverAddr),
		logger.Int("port", port))

	// Clear previously resolved server IP address list
	m.serverIPs = nil

	// Try to parse server address as IP address
	serverIP := net.ParseIP(m.config.ServerAddress)
	if serverIP != nil {
		// If server address is already an IP address, use it directly
		m.serverIPs = append(m.serverIPs, serverIP)
		m.log.Debug("Server address is already an IP",
			logger.String("ip", serverIP.String()))
	} else {
		// If server address is not an IP address, try to resolve it
		ips, err := net.LookupIP(m.config.ServerAddress)
		if err != nil {
			return m.cleanupWithError(fmt.Errorf("failed to resolve server address: %w", err),
				tunnel.ErrorConnectFailed, "failed to resolve server address: "+err.Error())
		}

		if len(ips) == 0 {
			return m.cleanupWithError(fmt.Errorf("no IP addresses found for server address: %s", m.config.ServerAddress),
				tunnel.ErrorConnectFailed, "no IP addresses found for server address")
		}

		// Save all resolved IP addresses
		for _, ip := range ips {
			// Only save IPv4 addresses
			if ip.To4() != nil {
				m.serverIPs = append(m.serverIPs, ip)
				m.log.Debug("Resolved server IP",
					logger.String("ip", ip.String()))
			}
		}

		if len(m.serverIPs) == 0 {
			return m.cleanupWithError(fmt.Errorf("no IPv4 addresses found for server address: %s", m.config.ServerAddress),
				tunnel.ErrorConnectFailed, "no IPv4 addresses found for server address")
		}
	}

	// Use first IP address for connection
	addr := &net.UDPAddr{
		IP:   m.serverIPs[0],
		Port: port,
	}

	// Set state to server IP obtained
	m.stateMachine.SetState(StateInit1)

	// Connect to server
	m.log.Info("Connecting to server",
		logger.String("address", addr.String()),
		logger.Int("resolved_ips", len(m.serverIPs)))
	m.conn, err = net.DialUDP("udp", nil, addr)
	if err != nil {
		return m.cleanupWithError(fmt.Errorf("failed to connect to server: %w", err),
			tunnel.ErrorConnectFailed, "failed to connect to server: "+err.Error())
	}

	// Physical interface info is now obtained on-demand via GetInterfaceInfo() method, no need to pre-fetch during connection

	// Set state to authenticating
	m.stateMachine.SetState(StateAuth)

	// Start packet receiver goroutine
	_, err = m.goroutineManager.Start("packet-receiver", func(ctx context.Context) error {
		m.receivePackets(ctx)
		return nil
	})
	if err != nil {
		return m.cleanupWithError(fmt.Errorf("failed to start packet receiver: %w", err),
			tunnel.ErrorConnectFailed, "failed to start packet receiver")
	}

	// Send authentication request
	err = m.authenticate()
	if err != nil {
		return m.cleanupWithError(fmt.Errorf("authentication failed: %w", err),
			tunnel.ErrorAuthFailed, "authentication failed: "+err.Error())
	}

	// Configure TUN device if available
	if m.tunDevice != nil {
		// Configure TUN device
		err = m.configureTunDevice(m.stateMachine.config)
		if err != nil {
			m.log.Error("Failed to configure TUN device", logger.ErrorField(err))
			// Don't return error since authentication was successful
		}
	}

	// Start heartbeat goroutine
	m.heartbeatTicker = time.NewTicker(m.config.HeartbeatInterval)
	_, err = m.goroutineManager.Start("heartbeat-sender", func(ctx context.Context) error {
		m.sendHeartbeats(ctx)
		return nil
	})
	if err != nil {
		return m.cleanupWithError(fmt.Errorf("failed to start heartbeat sender: %w", err),
			tunnel.ErrorConnectFailed, "failed to start heartbeat sender")
	}

	// Start heartbeat timeout detection goroutine
	_, err = m.goroutineManager.Start("heartbeat-monitor", func(ctx context.Context) error {
		m.monitorHeartbeat(ctx)
		return nil
	})
	if err != nil {
		return m.cleanupWithError(fmt.Errorf("failed to start heartbeat monitor: %w", err),
			tunnel.ErrorConnectFailed, "failed to start heartbeat monitor")
	}

	// Start TUN device reader goroutine
	_, err = m.goroutineManager.Start("tun-reader", func(ctx context.Context) error {
		m.runTunReader(ctx)
		return nil
	})
	if err != nil {
		return m.cleanupWithError(fmt.Errorf("failed to start TUN reader: %w", err),
			tunnel.ErrorConnectFailed, "failed to start TUN reader")
	}

	// Set current server information if not already set
	if m.currentServer == nil {
		// Create a basic server information object
		m.currentServer = &server.Server{
			ServerName: m.config.ServerAddress,
			ServerPort: m.config.ServerPort,
			Name:       m.config.ServerAddress, // Use address as name
			Status:     "online",
		}
		m.log.Info("Set basic server information in AuthAndConnect",
			logger.String("server_address", m.config.ServerAddress),
			logger.Int("server_port", m.config.ServerPort))
	} else {
		// If server info is already set, only update address and port, keep other info (like ID, Name, NameEn, etc.)
		m.currentServer.ServerName = m.config.ServerAddress
		m.currentServer.ServerPort = m.config.ServerPort
		m.log.Info("Updated server address and port in existing server info",
			logger.String("server_id", m.currentServer.ID),
			logger.String("server_name", m.currentServer.Name),
			logger.String("server_name_en", m.currentServer.NameEn),
			logger.String("server_address", m.config.ServerAddress),
			logger.Int("server_port", m.config.ServerPort))
	}

	m.log.Info("Successfully connected to server",
		logger.String("server", m.config.ServerAddress),
		logger.String("username", m.config.Username))

	return nil
}

/*****************************************************************************
 * NAME: Disconnect
 *
 * DESCRIPTION:
 *     Disconnect VPN connection
 *
 * RETURNS:
 *     error - Disconnect error if any
 *****************************************************************************/
func (m *Manager) Disconnect() error {
	// 	m.mutex.Lock()
	// 	defer m.mutex.Unlock()

	return m.disconnectInternal()
}

/*****************************************************************************
 * NAME: Shutdown
 *
 * DESCRIPTION:
 *     Completely shutdown connection manager, including releasing TUN device resources
 *
 * RETURNS:
 *     error - Shutdown error if any
 *****************************************************************************/
func (m *Manager) Shutdown() error {
	// First disconnect
	err := m.Disconnect()
	if err != nil {
		m.log.Error("Failed to disconnect during shutdown", logger.ErrorField(err))
		// Continue execution, don't interrupt shutdown process due to disconnect failure
	}

	// Close TUN device
	if m.tunDevice != nil {
		m.log.Info("Closing TUN device")
		// Don't close device directly, use TUN subsystem's Shutdown method instead
		// This ensures all related resources are properly released
		err := tun.Shutdown()
		if err != nil {
			m.log.Error("Failed to shutdown TUN subsystem", logger.ErrorField(err))
			return fmt.Errorf("failed to shutdown TUN subsystem: %w", err)
		}
		m.tunDevice = nil
		m.log.Info("TUN device closed successfully")
	}

	// Clean up other resources
	m.stateMachine = nil
	m.encryptor = nil
	m.currentServer = nil

	m.log.Info("Connection manager shutdown complete")
	return nil
}

// Status management methods moved to connection_status.go
// Traffic stats methods moved to connection_stats.go

// Data transmission methods moved to connection_data.go

// TUN device configuration and management methods moved to connection_tun.go

// ReceiveData method moved to connection_data.go

// Authentication method moved to connection_auth.go

// Packet processing methods moved to connection_packets.go

// Routing management methods moved to connection_routing.go

/*****************************************************************************
 * NAME: Stop
 *
 * DESCRIPTION:
 *     Stop connection manager and clean up all resources
 *
 * RETURNS:
 *     error - Stop error if any
 *****************************************************************************/
func (m *Manager) Stop() error {
	// 	m.mutex.Lock()
	// 	defer m.mutex.Unlock()

	if !m.started {
		return nil
	}

	m.log.Info("Stopping connection manager")

	// Disconnect
	if m.connected {
		m.disconnectInternal()
	}

	// Stop all goroutines
	if err := m.goroutineManager.StopAll(10 * time.Second); err != nil {
		m.log.Error("Failed to stop all goroutines", logger.ErrorField(err))
	}

	// Stop performance monitor
	if err := m.perfMonitor.Stop(); err != nil {
		m.log.Error("Failed to stop performance monitor", logger.ErrorField(err))
	}

	// Cancel context
	if m.cancel != nil {
		m.cancel()
		m.cancel = nil
	}

	m.started = false
	m.log.Info("Connection manager stopped successfully")
	return nil
}

/*****************************************************************************
 * NAME: disconnectInternal
 *
 * DESCRIPTION:
 *     Internal disconnect method (must be called while holding lock)
 *
 * RETURNS:
 *     error - Disconnect error if any
 *****************************************************************************/
func (m *Manager) disconnectInternal() error {
	if m.stateMachine == nil || m.stateMachine.GetState() == StateClosed {
		return nil
	}

	m.log.Info("Disconnecting from server")

	// 1. First stop accepting new data sends, but don't notify frontend immediately
	m.connected = false

	// 2. Send close packet (before connection closes)
	if m.conn != nil && m.encryptor != nil && m.stateMachine.GetState() == StateData {
		sessionID, token := m.stateMachine.GetSessionInfo()
		closePacket, err := tunnel.CreateClosePacket(sessionID, token, uint8(m.encryptor.Method()))
		if err == nil {
			m.sendPacketInternal(closePacket)
			// Give server some time to process close packet
			time.Sleep(100 * time.Millisecond)
		}
	}

	// 3. Cancel context, notify all goroutines to stop
	if m.cancel != nil {
		m.cancel()
		m.cancel = nil
	}

	// 4. Stop heartbeat timer
	if m.heartbeatTicker != nil {
		m.heartbeatTicker.Stop()
		m.heartbeatTicker = nil
	}

	// 5. Close network connection
	if m.conn != nil {
		if err := m.conn.Close(); err != nil {
			m.log.Error("Failed to close connection", logger.ErrorField(err))
		}
		m.conn = nil
	}

	// 6. Clean up TUN device configuration
	m.cleanupTunDeviceConfiguration()

	// 7. Reset traffic statistics
	m.resetTrafficStats()
	m.log.Debug("Traffic stats reset on disconnect")

	m.log.Info("Disconnect cleanup completed")

	// 8. Finally set state to closed (ensure all cleanup work is complete)
	// This will trigger state change callback, notify frontend that it's safe to initiate new connection
	if m.stateMachine != nil {
		m.stateMachine.SetState(StateClosed)
	}

	m.log.Info("Disconnected from server, ready for new connection")
	return nil
}

// Network change handling method moved to connection_reconnect.go
